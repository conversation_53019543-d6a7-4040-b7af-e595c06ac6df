For all of the below error messages we should tell the user that the photo might be perfectly OK and there might be no reason to retake it.

Message 1: Phone tilted? -- IMPLEMENTED & VERIFIED
Check: If area of the quadrilateral of final background_box is smaller than the minimum bounding box 

Message 2: Phone too close? (d cm) -- IMPLEMENTED & VERIFIED
Check: Estimate the distance of camera to the floor and if it smaller than 45 cm, then signal error 
       We are given FOV of the camera and the zoom factor z that was used to take the photo.
       We assume that A4 paper was used. Let d (in mm) be the distance of the camera to the object.
       From python, we can compute the size of the long side of the photo as L (in mm). We then have
       the relationship d=L*z/tan((FOV/360.0)*Pi). If d<500 (mm) then we should give a warning  

Message 3: Background paper properly detected? -- IMPLEMENTED & VERIFIED
Check: If the hough line detection gave < 4 lines:
       
Message 4: No contrast with floor/Blurry Image? -- IMPLEMENTED
Check: If the hough line detection gave <= 1 line       
       
Message 5: Background paper out of image? -- IMPLEMENTED & VERIFIED
Check: If the final rectangle has all corner points inside the image or not OR 
       If the hough line detection gave >= 2 and less than 4 lines AND backup background box is close to image boundary 

Message 6: Zoom in? -- IMPLEMENTED & VERIFIED
Check: If the area of the white paper is smaller than 1/8th of the photo area 

Message 7: Tool too close or extends beyond paper boundary?  -- IMPLEMENTED & VERIFIED
Check: If the closest point of any tool path is closer than 5mm from the white paper boundary.
       
Message 8: N tools? -- IMPLEMENTED & VERIFIED
Check: If we detect several reasonable tool outlines.

Message 9: Photo too blurry or tool has no contrast with paper? -- IMPLEMENTED
Check: If contour detection (after paper detection) gives no outline.

-- TODO --

Message: Background has strong pattern?
Check: If we get some messy patterns outside the white paper? Not clear how to check this.

Message: Tool outline messy?
Check: Perhaps we can check if the tool outline is very irregular, has many points or anything like this.

