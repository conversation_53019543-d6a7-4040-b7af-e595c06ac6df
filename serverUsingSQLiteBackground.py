from flask import Flask, request, Response, render_template
from flask_cors import CORS
import jsonpickle
import numpy as np
import cv2
import base64
import io
from PIL import Image
#import main
import imgProcFunctions_v3
#postgres library
import psycopg2
from datetime import datetime
import json
import string
import random
import os, boto3
from botocore.exceptions import NoCredentialsError
import time
import sys
import math
from botocore.exceptions import ClientError
import socket
import sqlite3
import tkinter as tk
from tkinter import messagebox
import tkinter.simpledialog as sd
import tkinter.font as font

sys.stdout = sys.stderr = open('log.txt','wt')

global MYHOSTIPANDPORT # this stores the server IP address and port number in the format - IPaddress:PortNumber
MYHOSTIPANDPORT = "IP_address:Port_number"
USER_INPUT_FOR_SERVER_ADDRESS = True # set this to false to just figure out localhost IP from ipconfig etc. or use 127.0.0.1

# are we using cable or WiFi over internal network
if imgProcFunctions_v3.MYENVIRONMENT != 'HEROKU':
    USE_CABLE = False # set to TRUE if phone is connected with cable to machine; set to FALSE if phone uses WiFi to connect to server via internal network

# postgres database environment variable
if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
    DATABASE_URL = os.environ['DATABASE_URL']
    # Amazon S3 access
    S3_BUCKET_NAME = os.environ.get('S3_BUCKET_NAME')
    
if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL_W_SQLITE':
    S3_BUCKET_NAME = 'toolkaiser'
    ACCESS_KEY = '********************'
    SECRET_KEY = '4/EcoiSTPxQASMv0ssJJT9b96mnVSaPvueCpybBJ'

# take in base64 string and return PIL image
def stringToImage(base64_string):
    imgdata = base64.b64decode(base64_string)
    return Image.open(io.BytesIO(imgdata))

# convert PIL Image to an RGB image (technically a numpy array) that's compatible with opencv
def toRGB(image):
    return cv2.cvtColor(np.array(image), cv2.COLOR_BGR2RGB)

# function to upload file to S3
def upload_to_aws(local_file, bucket, s3_file):
    if imgProcFunctions_v3.MYENVIRONMENT != 'HEROKU':
        s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    else:
        s3 = boto3.client('s3')
    try:
        s3.upload_file(local_file, bucket, s3_file)
        print("Upload Successful")
        return True
    except FileNotFoundError:
        print("The file was not found")
        return False
    except NoCredentialsError:
        print("Credentials not available")
        return False
    
# function to download file from S3 and save to local_file
def download_from_aws(local_file, bucket, s3_file):
    if imgProcFunctions_v3.MYENVIRONMENT != 'HEROKU':
        s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    else:
        s3 = boto3.client('s3')
    try:
        s3.download_file(bucket, s3_file, local_file)
        print("Download Successful")
        return True
    except FileNotFoundError:
        print("The file was not found")
        return False
    except NoCredentialsError:
        print("Credentials not available")
        return False

# check if file exists on s3
def check(bucket, key):
    if imgProcFunctions_v3.MYENVIRONMENT != 'HEROKU':
        s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    else:
        s3 = boto3.client('s3')
    try:
        s3.head_object(Bucket=bucket, Key=key)
    except ClientError as e:
        return int(e.response['Error']['Code']) != 404
    return True

# function to delete a file from S3
def delete_s3_file(bucket, s3_file): 
    if imgProcFunctions_v3.MYENVIRONMENT != 'HEROKU':
        s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    else:
        s3 = boto3.client('s3')
    s3.delete_object(Bucket=bucket, Key=s3_file)
    print("Deleting %s" % s3_file)    

# function to insert into sqlite database for STORAGE
def upload_to_sqlite(ID, fileType, data):
    try:
        con = sqlite3.connect("toolKaiserStorage.db")
        # cursor
        cur = con.cursor()
        # ID will the filename of the image or the canvas etc.
        # type will denote if the file is an image file or a json file or a txt file
        # data will hold the actual data: for images it will hold the b64 string; for json and text it will also hold the string
        cur.execute("insert into storageDB (id, fileType, data) values (?, ?, ?)", (ID, fileType, data))  
        # commit the transaction 
        con.commit()        
        # close the cursor
        cur.close()
        # close the connection
        con.close()
    except sqlite3.Error as error:
        print("Failed to insert data into sqlite table", error)
        return False            
    return True

# function to extract from the sqlite database from STORAGE
def download_from_sqlite(ID, fileType):
    
    downloaded = True
    try:
        con = sqlite3.connect("toolKaiserStorage.db")
        
        # cursor
        cur = con.cursor()
        # get row matching the ID and the fileType
        cur.execute("SELECT data FROM storageDB WHERE id = "+"\'"+ID+"\'"+" AND "+ "fileType = "+"\'"+fileType+"\'") 
        
        # this is a list of tuples (rownumber, photoID)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                        
        # jsonID is basically the canvasID (in s3 this is stored as .json)
        string_fromSQLite = str(rows[0][0])
    except sqlite3.Error as error:
        print("Failed to fetch data from sqlite table", error)
        downloaded = False
        string_fromSQLite = ""
    return downloaded,string_fromSQLite
    
# check if file exists on sqlite STORAGE       
def checkSQLite(ID, fileType):
    doesExists = False
    try:
        con = sqlite3.connect("toolKaiserStorage.db")
        
        # cursor
        cur = con.cursor()
        # get row matching the ID and the fileType
        cur.execute("SELECT id FROM storageDB WHERE id = "+"\'"+ID+"\'"+" AND "+ "fileType = "+"\'"+fileType+"\'") 
        
        # this is a list of tuples (rownumber, photoID)
        rows = cur.fetchone()
        
        if len(rows) > 0: # file doesn't exists
            doesExists = True
            
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
    except sqlite3.Error as error:
        print("Failed to check if ID exists or not from sqlite table", error)
    return doesExists
    

# Initialize the Flask application
app = Flask(__name__)
cors = CORS(app, resources={r"/api/*": {"origins": "*"}})

# route http posts to this method
@app.route('/api/sendImg', methods=['POST'])
def sendImg ():
    r = request # this is the incoming request    
    jsondata = r.get_json() 
    #print("jsondata:= ", jsondata)
    # get task type
    task = jsondata["task"]
            
    if task == "processPhoto" or task == "processPhotoFromEditor":

        # check if there is userEmail field
        if "userEmail" in jsondata:
            print('userEmail from processPhoto task:=', jsondata["userEmail"])
        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]
        print("fileName:= ", jsondata["fileName"])    
        
        # get FOV data
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            fov = jsondata["FOV"]
            ZoomFactor = jsondata["ZoomFactor"]
        else:
            fov = 57.72
            ZoomFactor = 1.0
        # generate a random string for filename to be stored in database and upload to S3
        photoID = ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))
        #photoID += '.jpg'
    
        # get filename name
        fileName = jsondata["fileName"]
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"]
        # get toolboxnumber if it exists
        toolboxNumber = 1
        if "toolboxNumber" in jsondata:
            toolboxNumber = jsondata["toolboxNumber"]
        
        # default backgroundPaperSize for appVersionNumber = 1.1.0 is A4
        # default backgroundPaperSize when upload from local disk is Letter
        backgroundPaperSize = "Letter"
        if task == "processPhoto":
            # get appVersionNumber
            appVersionNumber = jsondata["appVersionNumber"] 
            # get the backgroundPaperSize                
            if appVersionNumber >= "1.2.0":            
                backgroundPaperSize = jsondata["backgroundPaperSize"]
            if appVersionNumber == "1.1.0":            
                backgroundPaperSize = "A4"
            
        # in incoming string is encoded as b64  
        b64_string = jsondata["img"]
        # convert this first into a PIL image
        image = stringToImage(b64_string)
        # convert into opencv compatible format
        cvImage = toRGB(image)
        # write to a file to be uploaded to S3
        print('photoID:-', photoID)
        cv2.imwrite(photoID+'.jpg',cvImage)   
        
        # upload to S3 if using Heroku
        if imgProcFunctions_v3.MYSTORAGE == 's3': # use s3 to store images, canvas files etc.
            uploaded = upload_to_aws(photoID+'.jpg', S3_BUCKET_NAME, 'public/'+photoID+'.jpg')
        # upload to sqlite3 database
        if imgProcFunctions_v3.MYSTORAGE == 'sqlite': # use sqlite to store images, canvas files etc.
            uploaded = upload_to_sqlite(photoID,'jpg',b64_string) # store the image as a b64 string
        
        rows = cvImage.shape[0]
        columns = cvImage.shape[1]
    
        # image/ml processing to get the contours/outlines/convex hull    
        if imgProcFunctions_v3.MYENVIRONMENT == 'CRAWL':
            cvImageHighRes, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, myPaperSize, myWidthInmm, myHeightInmm, cvImageWithOutlines, errorMessage = imgProcFunctions_v3.proc_img_from_web(cvImage)
        else:
            cvImageHighRes, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, myPaperSize, myWidthInmm, myHeightInmm, cvImageWithOutlines, errorMessage = imgProcFunctions_v3.get_contours(cvImage, fov, ZoomFactor, backgroundPaperSize)            
        #cv2.imwrite('after_python_processing.jpg',cvImage)
                
        # now convert back into b64 format
        # if the task is from the editor 
        if task == "processPhoto":            
            jpegImage = cv2.imencode('.jpg', cvImage)[1].tostring()
        else:
            jpegImage = cv2.imencode('.jpg', cvImageHighRes)[1].tostring()
            
        b64Image = base64.b64encode(jpegImage)
        jpegImageWithOutlines = cv2.imencode('.jpg', cvImageWithOutlines)[1].tostring()
        b64ImageWithOutlines = base64.b64encode(jpegImageWithOutlines)
        
        # response is in json format to be sent back to javascript file
        response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'jpegWithOutlines': '{}'.format(b64ImageWithOutlines), 'photoID': photoID, 'errorMessage': errorMessage, 'toolboxNumber': toolboxNumber}
        print("errorMessage:=", errorMessage)

        # providing hooks for object recognition message to iPhone from pythonKaiser
        # right now we just have an empty message and the actual errorMessage
        if task == "processPhoto":
            if appVersionNumber >= "1.2.0":            
                if errorMessage == "":
                    type = "regular"
                    errorMessage = "Looking fabulous! Swipe to check the next tool. [Paper size: " + myPaperSize + "]"
                else:
                    type = "error"
                response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'jpegWithOutlines': '{}'.format(b64ImageWithOutlines), 'photoID': photoID, 'message': errorMessage, 'messageType': type, 'toolboxNumber': toolboxNumber}                
                    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
    
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL_W_SQLITE':        
            # connect to the database
            # for localhost server and local database
            if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
                con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
            elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
                con = psycopg2.connect(DATABASE_URL, sslmode='require')
            else:
                con = sqlite3.connect("toolKaiserDB.db")
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            #cur.execute("insert into userDataDB (toolKaiserid, photoid, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, photoID, fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor))  
            if imgProcFunctions_v3.MYENVIRONMENT != 'LOCAL_W_SQLITE':
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize, toolboxnumber) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, photoID, 'p', fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor, backgroundPaperSize, toolboxNumber))  
            else:
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize, toolboxnumber) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", (toolKaiserID, photoID, 'p', fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor, backgroundPaperSize, toolboxNumber))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.jpg which are older than 1 minutes
            ten_minutes_ago = time.time() - 60
            for fname in os.listdir('.'):
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "processToolKaiserID":       
        # get all the photoIDs from the database which match the toolKaiserID
        
        # response is in json format to be sent back to javascript file
        toolKaiserID = jsondata["toolKaiserID"]
        
        # get the startDate to fetch the images from 
        startDate = jsondata["startDate"]
        print('startDate:= ', startDate)
    
        # connect to the database
        # for localhost server and local database
        if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
            con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
        elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
        else:
            con = sqlite3.connect("toolKaiserDB.db")
            
        # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs starting from startDate till today 
        #cur.execute("SELECT photoid FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+"CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)") 
        cur.execute("SELECT id FROM userDataDB WHERE status=\'active\' AND toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+ "type = \'p\'" + " AND "+ "CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)")         
        
        # this is a list of tuples (rownumber, photoID, backgroundpapersize)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
            
        photoIDs = []        
        for r in rows:
            #print(type(r[0]), r[0])
            photoIDs.append(str(r[0]))            
            
        # use the toolKaiserID to get data from postgres
        response = {'photoIDs': photoIDs}    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "processPhotoID":
        
        # generate a random string for filename to be stored in database and upload to S3
        photoID = jsondata["photoID"]
        print("photoID:= ", photoID) 
        
        ### connect to the database and pull the backgroundPaperSize associated to the photoID
        
        # connect to the database
        # for localhost server and local database
        if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
            con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
        elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
        else:
            con = sqlite3.connect("toolKaiserDB.db")
            
        # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs starting from startDate till today 
        #cur.execute("SELECT photoid FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+"CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)") 
        cur.execute("SELECT backgroundpapersize, toolboxnumber FROM userDataDB WHERE id = "+"\'"+photoID+"\'")         
        
        # this is a list of tuples (rownumber, photoID, backgroundpapersize)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
            
        backgroundPaperSize = [] 
        #toolboxNumber = []
        for r in rows:
            #print(type(r[0]), r[0])
            backgroundPaperSize.append(str(r[0])) 
            toolboxNumber = r[1]
        
        # we download the corresponding file from AWS bucket and call processPhoto
        #downloaded = download_from_aws('cvImage.jpg', S3_BUCKET_NAME, 'public/'+photoID+'.jpg')
        #cvImage = cv2.imread('cvImage.jpg')  
        if imgProcFunctions_v3.MYSTORAGE == 's3': # use s3 to download images, canvas etc.
            downloaded = download_from_aws(photoID+'.jpg', S3_BUCKET_NAME, 'public/'+photoID+'.jpg')
            cvImage = cv2.imread(photoID+'.jpg') 
        # download from sqlite database
        if imgProcFunctions_v3.MYSTORAGE == 'sqlite': # use sqlite to download images, canvas etc.
            downloaded, b64_string_fromSQLite = download_from_sqlite(photoID, 'jpg')
            # convert this first into a PIL image
            image = stringToImage(b64_string_fromSQLite)
            # convert into opencv compatible format
            cvImage = toRGB(image)
                      
        fov = 0
        ZoomFactor = 1.0
        # image/ml processing to get the contours/outlines/convex hull    
        if imgProcFunctions_v3.MYENVIRONMENT == 'CRAWL':
            cvImageHighRes, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, myPaperSize, myWidthInmm, myHeightInmm, cvImageWithOutlines, errorMessage = imgProcFunctions_v3.proc_img_from_web(cvImage)
        else:
            cvImageHighRes, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, myPaperSize, myWidthInmm, myHeightInmm, cvImageWithOutlines, errorMessage = imgProcFunctions_v3.get_contours(cvImage, fov, ZoomFactor, backgroundPaperSize[0])            
        #cv2.imwrite('after_python_processing.jpg',cvImage)
        
        # now convert back into b64 format
        jpegImage = cv2.imencode('.jpg', cvImageHighRes)[1].tostring()
        b64Image = base64.b64encode(jpegImage)
        jpegImageWithOutlines = cv2.imencode('.jpg', cvImageWithOutlines)[1].tostring()
        b64ImageWithOutlines = base64.b64encode(jpegImageWithOutlines)

        # connect to the database to get the filename corresponding to the photoID
        # for localhost server and local database
        if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
            con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
        elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
        else:
            con = sqlite3.connect("toolKaiserDB.db")
            
        # cursor
        cur = con.cursor()
        
        #cur.execute("SELECT filename FROM userDataDB WHERE photoid = "+"\'"+photoID+"\'") 
        cur.execute("SELECT filename FROM userDataDB WHERE id = "+"\'"+photoID+"\'") 
        
        # this is a list of tuples (rownumber, photoID)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                            
        #print(type(r[0]), r[0])
        fileName = str(rows[0][0])
        print('fileName:= ', fileName)
        
        #if fileName == "":            
        fileName = str(photoID)
        print('fileName:= ', fileName)

        # response is in json format to be sent back to javascript file
        if "withOutlines" in jsondata:
            if jsondata["withOutlines"] == True:
                response = {'jpegWithOutlines': '{}'.format(b64ImageWithOutlines), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'fileName': fileName, 'errorMessage': errorMessage, 'toolboxNumber': toolboxNumber}        
            else:
                response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'fileName': fileName, 'errorMessage': errorMessage, 'toolboxNumber': toolboxNumber}
        else:
            response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'fileName': fileName, 'errorMessage': errorMessage, 'toolboxNumber': toolboxNumber}
            
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)

        # remove any .jpg files which are older than 1 minute
        ten_minutes_ago = time.time() - 60
        for fname in os.listdir('.'):
            if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"):
                st=os.stat(fname)
                mtime=st.st_mtime
                if mtime < ten_minutes_ago:
                    print('removed %s'%fname)
                    os.remove(fname)                    
    
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "dropPhoto":  
        
        # delete the row corresponding to photoID from database and also delete photo from s3
        photoID = jsondata["photoID"]
        toolKaiserID = jsondata["toolKaiserID"]
        print("photoID:= ", photoID)            
        
        # connect to the database to get the filename corresponding to the photoID
        # for localhost server and local database
        if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
            con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
        elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
        else:
            con = sqlite3.connect("toolKaiserDB.db")
            
        # cursor
        cur = con.cursor()
        
        #cur.execute("DELETE from userDataDB WHERE photoid = "+"\'"+photoID+"\'"+" AND "+"toolKaiserid = "+"\'"+toolKaiserID+"\'") 
        # mark the photo as deleted rather than actually deleting it
        cur.execute("UPDATE userDataDB SET status='deleted' WHERE id = "+"\'"+photoID+"\'"+" AND "+"toolKaiserid = "+"\'"+toolKaiserID+"\'") 
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                            
        # response is in json format to be sent back to javascript file
        response = {'comment': "file deleted from database and s3"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)

        # remove any .jpg files which are older than 1 minutes            
        ten_minutes_ago = time.time() - 60
        for fname in os.listdir('.'):
            if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"):
                st=os.stat(fname)
                mtime=st.st_mtime
                if mtime < ten_minutes_ago:
                    print('removed %s'%fname)
                    os.remove(fname)                        
                    
        # delete from S3        
        #delete_s3_file(S3_BUCKET_NAME, 'public/'+photoID+'.jpg')
    
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "saveCanvas":        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]        
    
        # generate a random string for canvasID to be stored in database and upload to S3
        canvasID = ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))        
    
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"] 
        # get canvasString
        canvasString = jsondata["canvasString"]            
        # get toolboxes
        toolboxes = jsondata["toolboxes"] 
        # get jpegData
        jpegData = jsondata["jpegData"] 

        # create JSON data from jpegData and toolboxes
        jpegToolBoxData = {}
        #svgToolBoxData['svgString'] = svgString
        jpegToolBoxData['toolboxes'] = toolboxes
        jpegToolBoxData['jpegData'] = jpegData
        
        #print('jpegData', jpegData)

        # write the the above JSON data to a file with name canvasID.json
        jpegFilename = canvasID+'.json'
        with open(jpegFilename, "w") as f:
            json.dump(jpegToolBoxData,f,indent=4,sort_keys=True)
        
        print('canvasID:-', canvasID)
                
        # upload to S3 if using Heroku
        if imgProcFunctions_v3.MYSTORAGE == 's3': # use s3 to store images, canvas files etc.                
            # write the canvasString to a file with name canvasID.txt
            canvasFilename = canvasID+'.txt'
            text_file = open(canvasFilename, "w")
            text_file.write('%s' % canvasString)
            text_file.close()  
            uploaded = upload_to_aws(canvasFilename, S3_BUCKET_NAME, 'public/'+canvasID+'.txt')
            uploaded = upload_to_aws(jpegFilename, S3_BUCKET_NAME, 'public/'+canvasID+'.json')
        # upload to sqlite3 database
        if imgProcFunctions_v3.MYSTORAGE == 'sqlite': # use sqlite to store images, canvas files etc.
            uploaded = upload_to_sqlite(canvasID, 'txt', canvasString) # store text file simply as a string
            uploaded = upload_to_sqlite(canvasID, 'json', json.dumps(jpegToolBoxData,indent=4,sort_keys=True)) # store the json as a string
            
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL_W_SQLITE':        
            # connect to the database
            # for localhost server and local database
            if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
                con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
            elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
                con = psycopg2.connect(DATABASE_URL, sslmode='require')
            else:
                con = sqlite3.connect("toolKaiserDB.db")
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            # set the backgroundPaperSize to be NA in this case since we have a cnavas of many tools which might have
            # different backgroundPaperSize
            #cur.execute("insert into userDataDB (toolKaiserid, photoid, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, photoID, fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor))  
            if imgProcFunctions_v3.MYENVIRONMENT != 'LOCAL_W_SQLITE':
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, canvasID, 'c', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
            else:
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", (toolKaiserID, canvasID, 'c', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.jpg which are older than 1 minutes
            ten_minutes_ago = time.time() - 60
            for fname in os.listdir('.'):
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        # response is in json format to be sent back to javascript file
        response = {'comment': "canvas uploaded to s3 and put in database"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "processOrderGivenToolKaiserID":       
        # get photos from the database which match the toolKaiserID for Wix order processing
        print('Inside processOrderGivenToolKaiserID')
        
        # response is in json format to be sent back to javascript file
        toolKaiserID = jsondata["toolKaiserID"]
                
        # connect to the database
        # for localhost server and local database
        if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
            con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
        elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
        else:
            con = sqlite3.connect("toolKaiserDB.db")
            
        # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs starting from startDate till today 
        #cur.execute("SELECT photoid FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+"CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)") 
        cur.execute("SELECT id FROM userDataDB WHERE status=\'active\' AND toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+ "type = \'c+p\'" + "order by herokutimestamp desc limit 1") 
        
        # this is a list of tuples (rownumber, photoID)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                        
        # jsonID is basically the canvasID (in s3 this is stored as .json)
        jsonID = str(rows[0][0])
        # get the filename without extension and version
        jsonIDWithoutVersion = jsonID.split('_v')[0] 
        # get the version number
        canvasVersionNumber = jsonID.split('_v')[1]
                
        # we download the corresponding JPEG JSON file from AWS bucket 
        jsonFilename = jsonIDWithoutVersion +'_JPEG_v' + canvasVersionNumber + '.json'
        print('before first downloaded')
        if imgProcFunctions_v3.MYSTORAGE == 's3': # use s3 to download images, canvas etc.
            # check if download was successful -- if not then download PNG file
            if check(S3_BUCKET_NAME, 'public/'+jsonFilename) == True:        
                downloaded = download_from_aws(jsonFilename, S3_BUCKET_NAME, 'public/'+jsonFilename)                    
            else:
                jsonFilename = jsonIDWithoutVersion +'_FOAM_v' + canvasVersionNumber + '.json'
                print('jsonFilename', jsonFilename)
                downloaded = download_from_aws(jsonFilename, S3_BUCKET_NAME, 'public/'+jsonFilename)
                print('second downloaded:=', downloaded)                    
            # read the JSON file & throw
            with open(jsonFilename) as f:
                jsonData = json.load(f)
        elif imgProcFunctions_v3.MYSTORAGE == 'sqlite': # use sqlite to download images, canvas etc.
            # check if download was successful -- if not then download PNG file
            if checkSQLite(jsonFilename.split('json')[0], 'json') == True:        
                downloaded,JSONstring_fromSQLite = download_from_sqlite(jsonFilename.split('json')[0], 'json')                    
            else:
                jsonFilename = jsonIDWithoutVersion +'_FOAM_v' + canvasVersionNumber + '.json'
                print('jsonFilename', jsonFilename)
                downloaded,JSONstring_fromSQLite = download_from_aws(jsonFilename.split('json')[0], 'json')
                print('second downloaded:=', downloaded)                    
            # read the JSON data
            jsonData = json.loads(JSONstring_fromSQLite)
        
        
        # also add the jsonID/canvasID to the jsonData
        jsonData["canvasID"] = jsonID
                
        # use the toolKaiserID to get data from postgres
        response = jsonData
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "saveCanvasOnCloud":        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]            
        # get the canvas
        canvasJSON = jsondata["canvasJSON"]
        # get the filename to be used to store in s3
        officialCanvasName = canvasJSON["officialCanvasName"]
        print("officialCanvasName:= ", officialCanvasName)
        # get the filename without extension
        officialCanvasNameWithoutExtension = officialCanvasName.split('.json')[0]
    
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"]     
                            
        # upload to S3    
        if imgProcFunctions_v3.MYSTORAGE == 's3': # use s3 to store images, canvas files etc.                   
            # write the the above JSON data to a file with name officialCanvasName.json        
            with open(officialCanvasName, "w") as f:
                json.dump(canvasJSON,f,indent=4,sort_keys=True)
            uploaded = upload_to_aws(officialCanvasName, S3_BUCKET_NAME, 'public/'+officialCanvasName)
        # upload to sqlite3 database
        if imgProcFunctions_v3.MYSTORAGE == 'sqlite': # use sqlite to store images, canvas files etc.            
            uploaded = upload_to_sqlite(officialCanvasNameWithoutExtension, 'json', json.dumps(canvasJSON,indent=4,sort_keys=True)) # store the json as a string
            
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL_W_SQLITE':        
            # connect to the database
            # for localhost server and local database
            if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
                con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
            elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
                con = psycopg2.connect(DATABASE_URL, sslmode='require')
            else:
                con = sqlite3.connect("toolKaiserDB.db")
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            # delete entries corresponding to the officialCanvasNameWithoutExtension
            cur.execute("DELETE from userDataDB WHERE id = "+"\'"+officialCanvasNameWithoutExtension+"\'"+" AND "+"type = "+"\'c\'") 
            
            # insert the new entry into the database
            if imgProcFunctions_v3.MYENVIRONMENT != 'LOCAL_W_SQLITE':
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
            else:
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.jpg which are older than 1 minutes
            ten_minutes_ago = time.time() - 60
            for fname in os.listdir('.'):
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        # response is in json format to be sent back to javascript file
        response = {'comment': "canvas autosaved to s3 and put in database"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "retrieveCanvasFromCloud":        
        
        # get the filename to be used to store in s3
        officialCanvasName = jsondata["officialCanvasName"]
        
        # if officialCanvasName is empty then look at canvasID and toolKaiserID
        if officialCanvasName == "":
            # get the canvasID
            canvasID = jsondata["canvasID"] 
            # find the latest version corresponding to canvasID from database                   
            # connect to the database
            # for localhost server and local database
            if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
                con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
            elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
                con = psycopg2.connect(DATABASE_URL, sslmode='require')
            else:
                con = sqlite3.connect("toolKaiserDB.db")
                
            # cursor
            cur = con.cursor()
            
            # get all the canvases correspoding to canvasID
            cur.execute("SELECT id FROM userDataDB WHERE id like "+"\'"+canvasID+"%\'" + " AND "+"type = "+"\'c\' ORDER BY herokutimestamp DESC")         
            # find the latest version number            
            
            # this is a list of tuples (rownumber, photoID, backgroundpapersize)
            rows = cur.fetchall()
            
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
            
            # close the cursor
            cur.close()
            
            # close the connection
            con.close()
            
            print('rows = ', rows, len(rows))
            if len(rows) > 0:
                officialCanvasID = rows[0][0]
                officialCanvasName = officialCanvasID + '.json'
            print('officialCanvasName from canvasID = ', officialCanvasName)
            if officialCanvasName == "":
                # get toolKaiser ID
                toolKaiserID = jsondata["toolKaiserID"] 
                # get the latest canvases                    
                canvasID = jsondata["canvasID"] 
                # find the latest version corresponding to canvasID from database                   
                # connect to the database
                # for localhost server and local database
                if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
                    con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
                elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
                    con = psycopg2.connect(DATABASE_URL, sslmode='require')
                else:
                    con = sqlite3.connect("toolKaiserDB.db")
                    
                # cursor
                cur = con.cursor()
                
                # get all the canvases correspoding to canvasID
                cur.execute("SELECT id FROM userDataDB WHERE toolkaiserid = "+"\'"+toolKaiserID+"\'" + " AND "+"type = "+"\'c\' ORDER BY herokutimestamp DESC")         
                # find the latest version number            
                
                # this is a list of tuples (rownumber, photoID, backgroundpapersize)
                rows = cur.fetchall()
                
                # commit the transaction otherwise you will not see it in pgadmin
                con.commit()
                
                # close the cursor
                cur.close()
                
                # close the connection
                con.close()
                
                print('rows = ', rows, len(rows))
                if len(rows) > 0:
                    officialCanvasID = rows[0][0]
                    officialCanvasName = officialCanvasID + '.json'
                print('officialCanvasName from toolkaiserID = ', officialCanvasName)
                                         
        print("officialCanvasName = ", officialCanvasName)
        
        # we download the corresponding file from AWS bucket and call processPhoto
        if imgProcFunctions_v3.MYSTORAGE == 's3': # use s3 to download images, canvas etc.
            downloaded = download_from_aws(officialCanvasName, S3_BUCKET_NAME, 'public/'+officialCanvasName)        
        elif imgProcFunctions_v3.MYSTORAGE == 'sqlite': # use sqlite to download images, canvas etc.
            downloaded,JSONstring_fromSQLite = download_from_sqlite(officialCanvasName.split('.json')[0], 'json')        
    
        if downloaded == True:         
            print("download successful")
            if imgProcFunctions_v3.MYSTORAGE == 's3':
                # read the canvas JSON file
                with open(officialCanvasName) as f:
                    canvasJSON = json.load(f)
            elif imgProcFunctions_v3.MYSTORAGE == 'sqlite':
                canvasJSON = json.loads(JSONstring_fromSQLite)
        
            # response is in json format to be sent back to javascript file
            response = {'canvasJSON': canvasJSON, 'errorMessage': "success"}
        
            # encode response using jsonpickle
            response_pickled = jsonpickle.encode(response)
        else:
            # response is in json format to be sent back to javascript file
            response = {'canvasJSON': {}, 'errorMessage': "file not found"}
        
            # encode response using jsonpickle
            response_pickled = jsonpickle.encode(response)

        # remove any .jpg or .json files which are older than 1 minutes            
        ten_minutes_ago = time.time() - 60
        for fname in os.listdir('.'):
            if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".JSON") or fname.endswith(".json"):
                st=os.stat(fname)
                mtime=st.st_mtime
                if mtime < ten_minutes_ago:
                    print('removed %s'%fname)
                    os.remove(fname)                    
    
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "saveCanvasAndJPEGOnCloud":        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]               
        
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"] 
        # get canvasString
        canvasJSON = jsondata["canvasJSON"]   
        # get the filename to be used to store in s3
        officialCanvasName = canvasJSON["officialCanvasName"]
        print("officialCanvasName:= ", officialCanvasName)
        # get the filename without extension
        officialCanvasNameWithoutExtension = officialCanvasName.split('.json')[0]  
        # get the filename without extension and version
        officialCanvasNameWithoutExtensionAndVersion = officialCanvasNameWithoutExtension.split('_v')[0] 
        # get the version number
        canvasVersionNumber = officialCanvasNameWithoutExtension.split('_v')[1]
        
        # get toolboxes
        toolboxes = jsondata["toolboxes"] 
        # get jpegData
        jpegData = jsondata["jpegData"] 
        
        # create JSON data from jpegData and toolboxes
        jpegToolBoxData = {}
        #svgToolBoxData['svgString'] = svgString
        jpegToolBoxData['toolboxes'] = toolboxes
        jpegToolBoxData['jpegData'] = jpegData
        
        #print('jpegData', jpegData)
        
        # write the the above JSON data to a file with name canvasID.json
        jpegFilename = officialCanvasNameWithoutExtensionAndVersion +'_JPEG_v' + canvasVersionNumber + '.json'
        
        print('officialCanvasNameWithoutExtension:-', officialCanvasNameWithoutExtension)
        
        # write the the above JSON data to a file with name officialCanvasName.json 
        orderCanvasName = officialCanvasNameWithoutExtensionAndVersion +'_ORDER_v' + canvasVersionNumber + '.json'
        # before dumping the JSON data change the officialCanvasName to include _ORDER_
        canvasJSON["officialCanvasName"] = orderCanvasName

                        
        # upload to S3    
        if imgProcFunctions_v3.MYSTORAGE == 's3': # use s3 to store images, canvas files etc. 
            with open(orderCanvasName, "w") as f:
                json.dump(canvasJSON,f,indent=4,sort_keys=True) 
            with open(jpegFilename, "w") as f:
                json.dump(jpegToolBoxData,f,indent=4,sort_keys=True)
            uploaded = upload_to_aws(orderCanvasName, S3_BUCKET_NAME, 'public/'+orderCanvasName)
            uploaded = upload_to_aws(jpegFilename, S3_BUCKET_NAME, 'public/'+jpegFilename)
        # upload to sqlite3 database
        if imgProcFunctions_v3.MYSTORAGE == 'sqlite': # use sqlite to store images, canvas files etc.            
            uploaded = upload_to_sqlite(orderCanvasName.split('.json')[0], 'json', json.dumps(canvasJSON,indent=4,sort_keys=True)) # store json as a string
            uploaded = upload_to_sqlite(jpegFilename.split('.json')[0], 'json', json.dumps(jpegToolBoxData,indent=4,sort_keys=True)) # store json as a string
            
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL_W_SQLITE':        
            # connect to the database
            # for localhost server and local database
            if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
                con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
            elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
                con = psycopg2.connect(DATABASE_URL, sslmode='require')
            else:
                con = sqlite3.connect("toolKaiserDB.db")
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            # delete entries corresponding to the officialCanvasNameWithoutExtension
            #cur.execute("DELETE from userDataDB WHERE id = "+"\'"+officialCanvasNameWithoutExtension+"\'"+" AND "+"type = "+"\'c+p\'") 
            
            # insert the new entry into the database
            if imgProcFunctions_v3.MYENVIRONMENT != 'LOCAL_W_SQLITE':
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c+p', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
            else:
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c+p', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.jpg which are older than 1 minutes
            ten_minutes_ago = time.time() - 60
            for fname in os.listdir('.'):
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        # response is in json format to be sent back to javascript file
        response = {'comment': "canvas and jpeg saved to s3 and put in database"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "saveCanvasAndFoamOnCloud":  
        
        # "foam" here represents generic data format: svgString, png, gif, jpeg
        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]               
        
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"] 
        # get canvasString
        canvasJSON = jsondata["canvasJSON"]   
        # get the filename to be used to store in s3
        officialCanvasName = canvasJSON["officialCanvasName"]
        print("officialCanvasName:= ", officialCanvasName)
        # get the filename without extension
        officialCanvasNameWithoutExtension = officialCanvasName.split('.json')[0]  
        # get the filename without extension and version
        officialCanvasNameWithoutExtensionAndVersion = officialCanvasNameWithoutExtension.split('_v')[0] 
        # get the version number
        canvasVersionNumber = officialCanvasNameWithoutExtension.split('_v')[1]
        
        # get toolboxes
        toolboxes = jsondata["toolboxes"] 
        # get foamData
        foamData = jsondata["foamData"] 
        
        # create JSON data from foamData and toolboxes
        foamToolBoxData = {}
        #svgToolBoxData['svgString'] = svgString
        foamToolBoxData['toolboxes'] = toolboxes
        foamToolBoxData['foamData'] = foamData
        
        #print('foamData', foamData)
        
        # write the the above JSON data to a file with name canvasID.json
        foamFilename = officialCanvasNameWithoutExtensionAndVersion +'_FOAM_v' + canvasVersionNumber + '.json'        
        
        print('officialCanvasNameWithoutExtension:-', officialCanvasNameWithoutExtension)
        
        # write the the above JSON data to a file with name officialCanvasName.json 
        orderCanvasName = officialCanvasNameWithoutExtensionAndVersion +'_ORDER_v' + canvasVersionNumber + '.json'
        # before dumping the JSON data change the officialCanvasName to include _ORDER_
        canvasJSON["officialCanvasName"] = orderCanvasName
                        
        # upload to S3    
        if imgProcFunctions_v3.MYSTORAGE == 's3': # use s3 to store images, canvas files etc.  
            with open(orderCanvasName, "w") as f:
                json.dump(canvasJSON,f,indent=4,sort_keys=True)  
            with open(foamFilename, "w") as f:
                json.dump(foamToolBoxData,f,indent=4,sort_keys=True)
            uploaded = upload_to_aws(orderCanvasName, S3_BUCKET_NAME, 'public/'+orderCanvasName)
            uploaded = upload_to_aws(foamFilename, S3_BUCKET_NAME, 'public/'+foamFilename)
        # upload to sqlite3 database
        if imgProcFunctions_v3.MYSTORAGE == 'sqlite': # use s3 to store images, canvas files etc.           
            uploaded = upload_to_sqlite(orderCanvasName.split('.json')[0], 'json', json.dumps(canvasJSON,indent=4,sort_keys=True)) # store json as a string
            uploaded = upload_to_sqlite(foamFilename.split('.json')[0], 'json', json.dumps(foamToolBoxData,indent=4,sort_keys=True)) # store json as a string 
            
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL_W_SQLITE':        
            # connect to the database
            # for localhost server and local database
            if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
                con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
            elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
                con = psycopg2.connect(DATABASE_URL, sslmode='require')
            else:
                con = sqlite3.connect("toolKaiserDB.db")
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            # delete entries corresponding to the officialCanvasNameWithoutExtension
            #cur.execute("DELETE from userDataDB WHERE id = "+"\'"+officialCanvasNameWithoutExtension+"\'"+" AND "+"type = "+"\'c+p\'") 
            
            # insert the new entry into the database
            if imgProcFunctions_v3.MYENVIRONMENT != 'LOCAL_W_SQLITE':
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c+p', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
            else:
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c+p', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.png which are older than 1 minutes
            ten_minutes_ago = time.time() - 60
            for fname in os.listdir('.'):
                if fname.endswith(".png") or fname.endswith(".PNG") or fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        # response is in json format to be sent back to javascript file
        response = {'comment': "canvas and foam saved to s3 and put in database"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        
        return Response(response=response_pickled, status=200, mimetype="application/json")   
    elif task == "processIDAndEmail":       
        # get toolKaiserID and email of the user
        
        # response is in json format to be sent back to javascript file
        toolKaiserID = jsondata["toolKaiserID"]
        # get the user email 
        userEmail = jsondata["userEmail"]    
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"]
        
        print('toolKaiserID, userEmail:= ', toolKaiserID, userEmail)
        
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL' or imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL_W_SQLITE':        
            # connect to the database
            # for localhost server and local database
            if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
                con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
            elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
                con = psycopg2.connect(DATABASE_URL, sslmode='require')
            else:
                con = sqlite3.connect("toolKaiserDB.db")
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            #cur.execute("insert into userDataDB (toolKaiserid, photoid, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, photoID, fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor))  
            if imgProcFunctions_v3.MYENVIRONMENT != 'LOCAL_W_SQLITE':
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize, useremail) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, '', 'e', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA", userEmail))  
            else:
                cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize, useremail) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", (toolKaiserID, '', 'e', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA", userEmail))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.png which are older than 1 minutes
            ten_minutes_ago = time.time() - 60
            for fname in os.listdir('.'):
                if fname.endswith(".png") or fname.endswith(".PNG") or fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
        
                                 
        # response is in json format to be sent back to javascript file
        response = {'comment': "heroku got user email and toolKaiserID"}    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "changeToolboxNumber":
        
        # generate a random string for filename to be stored in database and upload to S3
        photoID = jsondata["photoID"]
        toolboxNumber = jsondata["toolboxNumber"]
        print("photoID:= ", photoID) 
        print("toolboxNumber:= ", toolboxNumber) 
        
        ### connect to the database and pull the backgroundPaperSize associated to the photoID
        
        # connect to the database
        # for localhost server and local database
        if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL':
            con = psycopg2.connect(host="localhost", database="toolKaiserDB", user="postgres", password="Kudekar", port=5432)            
        elif imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
        else:
            con = sqlite3.connect("toolKaiserDB.db")
            
        # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs starting from startDate till today 
        #cur.execute("SELECT photoid FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+"CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)") 
        cur.execute("UPDATE userDataDB SET toolboxnumber="+ str(toolboxNumber) + " WHERE id = "+"\'"+photoID+"\'")                 
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
          
        # response is in json format to be sent back to javascript file
        response = {'comment': "photo moved to " + str(toolboxNumber)}     
        
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
    
        return Response(response=response_pickled, status=200, mimetype="application/json")        
    else:
        print("No task provided!")
        

@app.route('/')
def index():
    return render_template('index.html')

# start flask app
if __name__ == '__main__':
        
    # LICENSE and Terms and Conditions
    #print("#######################################################################")
    #print("#######################################################################")
    #print("#######################################################################")
    #print("#######################################################################")
    #print("#######################################################################")
    #print("BY ENTERING A SERVER ADDRESS AND PORT NUMBER BELOW YOU ARE ACCEPTING")
    #print("THE LICENSING AGREEMENT AND THE TERMS AND CONDITIONS. PLEASE READ")
    #print("\"LICENSE.TXT\" and \"TERMS AND CONDITIONS.TXT\" BEFORE PROCEEDING.")
    #print("#######################################################################")
    #print("#######################################################################")
    
    # Create the Tkinter window
    root = tk.Tk()
    root.geometry("700x350")
    root.withdraw()
    
    # Define a custom dialog box
    class CustomDialogBox(sd.Dialog):
        def __init__(self, parent, title, text, width=300, height=100):
            self.text = text
            self.width = width
            self.height = height
            super().__init__(parent, title=title)
        
        def body(self, master):
            tk.Label(master, text=self.text, font=(None, 14)).pack()
        
        def buttonbox(self):
            box = tk.Frame(self)
            tk.Button(box, text="Accept", font=(None, 14), command=self.accept).pack(side="left", padx=5, pady=5)
            tk.Button(box, text="Cancel", font=(None, 14), command=self.cancel).pack(side="left", padx=5, pady=5)
            self.bind("<Return>", self.ok)
            box.pack()
            
        def accept(self):
            self.result = "Accept"
            self.ok()
    
    # Show the custom dialog box
    dlg = CustomDialogBox(root, "LICENSE & TERMS AND CONDITIONS", "BY PRESSING \"ACCEPT\" BELOW YOU ARE \nACCEPTING THE LICENSING AGREEMENT\n AND THE TERMS AND CONDITIONS.\n PLEASE READ \"LICENSE.TXT\" and\n \"TERMS AND CONDITIONS.TXT\" BEFORE PROCEEDING.", width=400, height=150)
    print("result:= ", dlg.result)
    # Check the result of the dialog
    if dlg.result == "Accept":
        print("User clicked Accept")
        root.destroy()
    else:
        print("User closed the window or clicked Cancel")
        root.protocol("WM_DELETE_WINDOW", sys.exit(1)) # Quit the program if the user clicked Cancel or closed the window
    
    print("Environment:= ", imgProcFunctions_v3.MYENVIRONMENT)
    print("Storage:= ", imgProcFunctions_v3.MYSTORAGE)
    
    if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
        #app.use(express.static(path.join(__dirname, 'app/templates')))
        app.run()   
    else:
        print('python version:= ', sys.version)
        #app.run(host="*************", port=8000)
        
        # when we want to run things using the cable --> WiFi NOT required!! (this is for air-gaped implementation)
        if USE_CABLE == True:
            app.run(host="127.0.0.1", port=8000)
        else:    
            if USER_INPUT_FOR_SERVER_ADDRESS:
                #MYHOSTIPANDPORT = input('\n\nPlease enter the server address and port number. E.g., *************:8000 or 127.0.0.1:8000 \n\n')
                
                class InputDialogBox(tk.Toplevel):
                    def __init__(self, parent, title, prompt_text, placeholder_text):
                        super().__init__(parent)
                        self.title(title)
                        self.result = None
                        
                        # Create a label with the prompt text
                        prompt_label = tk.Label(self, text=prompt_text, font=(None, 14))
                        prompt_label.pack(side="top", padx=10, pady=10)
                        
                        # Create an Entry widget with the placeholder text
                        self.input_var = tk.StringVar()
                        self.input_var.set(placeholder_text)
                        input_entry = tk.Entry(self, textvariable=self.input_var, font=(None, 14))
                        input_entry.pack(side="top", padx=10, pady=10)
                        
                        # Create an Accept button
                        accept_button = tk.Button(self, text="Set", font=(None, 14), command=self.accept)
                        accept_button.pack(side="left", padx=5, pady=5)
                        
                        # Create a Cancel button
                        cancel_button = tk.Button(self, text="Cancel", font=(None, 14), command=self.cancel)
                        cancel_button.pack(side="right", padx=5, pady=5)
                        
                        # Bind the Return key to the Accept button
                        self.bind("<Return>", lambda event: self.accept())
                        
                        # Set the focus to the input field
                        input_entry.focus_set()
                        input_entry.selection_range(0, tk.END)
                    
                    def accept(self):
                        # Set the result attribute to the current value of the input field
                        self.result = self.input_var.get()
                        self.destroy()
                    
                    def cancel(self):
                        # Set the result attribute to None and close the dialog box
                        self.result = None
                        self.destroy()
                
                # Create the main Tkinter window
                root = tk.Tk()
                
                # Create a function to show the input dialog box and return the result                
                def show_input_dialog():
                    global MYHOSTIPANDPORT
                    # Create an instance of the InputDialogBox and wait for the user to close it
                    hostname = socket.getfqdn()                    
                    dialog = InputDialogBox(root, "Server Address & Port Number", "Please enter IP address and port number.\n It seems your IP address is: "+socket.gethostbyname_ex(hostname)[2][0]+".\n Please enter IPaddress:PortNumber below.", socket.gethostbyname_ex(hostname)[2][0]+":8000")
                    root.wait_window(dialog)
                    
                    # Check the result of the dialog and print it if it's not None
                    if dialog.result is not None:
                        MYHOSTIPANDPORT = dialog.result
                        print("User entered:= ", MYHOSTIPANDPORT) 
                        root.iconify()
                        root.destroy()
                    else:
                        sys.exit(1)
                
                # Create a button to show the input dialog box
                input_button = tk.Button(root, text="Set server IP address and port number", command=show_input_dialog)
                input_button['font'] = font.Font(size=14)
                input_button.pack(padx=10, pady=10)
                
                # Start the Tkinter event loop
                root.mainloop()   
                
                # now connect to the sqlite3 database and create the table if it doesn't exists
                if imgProcFunctions_v3.MYENVIRONMENT == 'LOCAL_W_SQLITE': 
                    con = sqlite3.connect("toolKaiserDB.db")
                    # cursor
                    cur = con.cursor()
                    cur.execute("CREATE TABLE IF NOT EXISTS userDataDB (toolKaiserID text, ID text, type text, filename text, platform text, browser text, userTimeStamp text, herokuTimeStamp text, rows integer, columns integer, fov numeric (5,2), zoomfactor numeric (4,2), backgroundPaperSize varchar(20) DEFAULT('A4'),  userEmail varchar(50) DEFAULT('NA'), status varchar(10) DEFAULT('active'), toolboxNumber int DEFAULT(1)); ")
                    # commit the transaction 
                    res = cur.execute("SELECT name FROM sqlite_master")
                    print("TABLE NAME:= ", res.fetchone())
                    con.commit()        
                    # close the cursor
                    cur.close()
                    con.close()
                    
                # STORAGE: now connect to the sqlite3 database and create the table if it doesn't exists
                if imgProcFunctions_v3.MYSTORAGE == 'sqlite': 
                    con = sqlite3.connect("toolKaiserStorage.db")
                    # cursor
                    cur = con.cursor()
                    # ID will the filename of the image or the canvas etc.
                    # type will denote if the file is an image file or a json file or a txt file
                    # data will hold the actual data: for images it will hold the b64 string; for json and text it will also hold the string
                    cur.execute("CREATE TABLE IF NOT EXISTS storageDB (ID text, fileType text, data text);")
                    # commit the transaction 
                    res = cur.execute("SELECT name FROM sqlite_master")
                    print("TABLE NAME:= ", res.fetchone())
                    con.commit()        
                    # close the cursor
                    cur.close()
                    con.close()
                
                myServerAddress = MYHOSTIPANDPORT.split(':')[0]
                myPort = MYHOSTIPANDPORT.split(':')[1]
                app.run(host=myServerAddress, port=myPort)     
            else:

                # use below to find automatically the IP address of the localhost server so that things can be
                # run over WiFi over the internal network (so NO external server such as Heroku required)
                hostname = socket.getfqdn()
                app.run(host=socket.gethostbyname_ex(hostname)[2][0], port=8000)
        
