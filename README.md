# pythonkaiser

# to run locally with dropbox, set the flag imgProcFunctions_v2.MYENVIRONMENT = 'PYTHON'
# press the 'run' button in python environment OR just run the command 'python imgProcFunctions_v2.py'

# to run on localhost server, set the flag imgProcFunctions_v2.MYENVIRONMENT = 'LOCAL'
# start server in pythonkaiser by running the command 'python server.py'

# to run on heroku server, set the flag imgProcFunctions_v2.MYENVIRONMENT = 'HEROKU'
# run ./pythonToHeroku.sh for pushing files onto herokukaiser; 'git commit' in herokukaiser; 'git push heroku master' in herokukaiser

# to download stuff from AWS run downloadFromAWS locally. If not toolKaiser id is provided, all the images in public/ (in AWS s3) is downloaded to dropbox folder. If toolKaiser id is provided then only those images corresponding that id are downloaded. Check the python script to see how to ensure proper running of the case when toolKaiser id is supplied
