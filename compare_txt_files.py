# -*- coding: utf-8 -*-
"""
Created on Tue Mar 10 11:21:23 2020

@author: SK & RU
"""
import numpy as np
import cv2
import os

PXPERMM = 3.77953 # guess about how many px make one mm on the screen
THRESH_BKGRND_BOX = 5 # bkgrnd_box error threshold
THRESH_OFFSETX = 5 # offsetY error threshold
THRESH_OFFSETY = 5 # offsetY error threshold
THRESH_PERIMETER = 10 # convexhull/cntr perimeter error threshold
THRESH_AREA = 0.01 # convexhull/cntr area error threshold ~ 1%
THRESH_DIST = 10 # convexhull/cntr distance error threshold

folder_txt_src = 'D://home//work//DigitalHome//DigitalToolBox//Test_txt_output_1'
#folder_txt_src = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools test txt'
folder_txt_err = 'D://home//work//DigitalHome//DigitalToolBox//Test_txt_error'
folder_txt_dst = 'D://home//work//DigitalHome//DigitalToolBox//Test_txt_output_2'


def get_content(filename, folder_txt):
        lines = open(os.path.join(folder_txt,filename+".txt")).read().splitlines()
        # size of image
        data = lines[0]
        data = lines[0].split(' ')
        sh_img_src = np.asarray([int(data[0]), int(data[1])],dtype = int)
        # background box; corners are bkgrnd_box_src[0], bkgrnd_box_src[1], bkgrnd_box_src[2], bkgrnd_box_src[3]
        data = lines[1]
        data = lines[1].split(' ')
        bkgrnd_box_src = []        
        for i in range(0,int(len(data)/2)):
            if data[2*i] == '#':
                break
            else:
                bkgrnd_box_src.append(np.asarray([float(data[2*i]),float(data[2*i+1])]))
        # number of tools
        data = lines[2]
        data = lines[2].split(' ')
        num_of_tools_src = int(data[0])
        # offset of each tool
        offsetX_src = []
        offsetY_src = []
        for i in range(0,num_of_tools_src):        
            data = lines[3+i]
            data = lines[3+i].split(' ')             
            offsetX_src.append(float(data[0]))
            offsetY_src.append(float(data[1]))
        # convex hull of each tool
        convexhulls_src = []
        num_pts_convexhull_src = []
        for i in range(0,num_of_tools_src):                    
            # get the number of pts in convex hull
            data = lines[3+num_of_tools_src+2*i]
            data = lines[3+num_of_tools_src+2*i].split(' ')             
            num_pts_convexhull_src.append(int(data[0]))
            # get the (x,y) of convex hull
            convexhull = []
            data = lines[3+num_of_tools_src+2*i+1]
            data = lines[3+num_of_tools_src+2*i+1].split(' ') 
            for j in range(0,int(len(data)/2)):
                if data[2*j] == '#':
                    break
                else:
                    convexhull.append(np.asarray([float(data[2*j]),float(data[2*j+1])]))                    
            convexhulls_src.append(convexhull)
        # contour for each tool
        cntrs_src = []
        num_pts_cntrs_src = []
        perimeter_cntrs_src = []
        area_cntrs_src = []
        for i in range(0,num_of_tools_src):                    
            # get the number of pts in convex hull
            data = lines[3+3*num_of_tools_src+2*i]
            data = lines[3+3*num_of_tools_src+2*i].split(' ')             
            num_pts_cntrs_src.append(int(data[0]))
            perimeter_cntrs_src.append(float(data[1]))
            area_cntrs_src.append(float(data[2]))
            # get the (x,y) of convex hull
            cntr = []
            data = lines[3+3*num_of_tools_src+2*i+1]
            data = lines[3+3*num_of_tools_src+2*i+1].split(' ') 
            for j in range(0,int(len(data)/2)):
                if data[2*j] == '#':
                    break
                else:
                    cntr.append(np.asarray([float(data[2*j]),float(data[2*j+1])]))                    
            cntrs_src.append(cntr)

        return sh_img_src, bkgrnd_box_src, num_of_tools_src, offsetX_src, offsetY_src, num_pts_convexhull_src, convexhulls_src, num_pts_cntrs_src, perimeter_cntrs_src, area_cntrs_src, cntrs_src

# perimeter length of the contour
def cntrperi(cnt):
   return cv2.arcLength(cnt,True)

# area of the contour
def cntrarea(cnt):
    return cv2.contourArea(cnt)

# compare the image sizes
def compare_sh_img(sh_img_src, sh_img_dst):
    diff_sh_img = np.asarray([sh_img_dst[0] - sh_img_src[0], sh_img_dst[1] - sh_img_src[1]])
    return diff_sh_img

# compare the bkgrnd boxes
def compare_bkgrnd_box(bkgrnd_box_src, bkgrnd_box_dst):
    if len(bkgrnd_box_src) != len(bkgrnd_box_dst):
        diff_bkgrnd_box = []
    else:
        diff_bkgrnd_box = np.linalg.norm(np.asarray(bkgrnd_box_dst) - np.asarray(bkgrnd_box_src), axis = 1)
    return np.round(diff_bkgrnd_box, 2)

# compare the num of tools
def compare_num_of_tools(num_of_tools_src, num_of_tools_dst):
    return num_of_tools_dst - num_of_tools_src
    
# compare the offsets
def compare_offset(num_of_tools, offsetX_src, offsetY_src, offsetX_dst, offsetY_dst):
    diff_offset = np.zeros((num_of_tools,2))
    for i in range(0,num_of_tools):
        diff_offset[i][0] = offsetX_dst[i] - offsetX_src[i]
        diff_offset[i][1] = offsetY_dst[i] - offsetY_src[i]
    return diff_offset

# compare the convex hulls
def compare_convexhulls(num_of_tools, num_pts_convexhull_src, convexhulls_src, num_pts_convexhull_dst, convexhulls_dst):          
    # right now we just compare the perimeter and area of the convexhull
    diff_convexhulls= []
    for i in range(0,num_of_tools):
        c = np.asarray((convexhulls_src[i]), dtype = np.float32)
        d = np.asarray((convexhulls_dst[i]), dtype = np.float32)
        peri_src = cntrperi(c)
        area_src = cntrarea(c)
        peri_dst = cntrperi(d)
        area_dst = cntrarea(d)
        diff_convexhulls.append(np.asarray([peri_dst - peri_src, area_dst - area_src, get_dist(convexhulls_src[i], convexhulls_dst[i]), get_dist(convexhulls_dst[i], convexhulls_src[i])], dtype = np.float32))
        #diff_convexhulls.append(np.asarray([get_dist(convexhulls_src[i], convexhulls_dst[i]), get_dist(convexhulls_dst[i], convexhulls_src[i])], dtype=np.float32))
    
    return diff_convexhulls

# get max of the Euclid dist of closest point on cntr_dst or convex_hull
def get_dist(src, dst):
    src = np.squeeze(src)
    dst = np.squeeze(dst)
    #print(cntr_src, len(cntr_src))
    #print(cntr_dst, len(cntr_dst))
    distances = []
    for i in range(0,len(src)):
        mindist = 1e10
        for j in range(0,len(dst)):            
            dist = np.linalg.norm(src[i] - dst[j])                        
            # if at any time, mindist <= pxpermm then we break; otherwise the two for loops take a while
            if dist <= PXPERMM:
                mindist = dist
                break
            if dist <= mindist:
                mindist = dist 
        distances.append(mindist)
    distances = np.asarray(distances)
    #print(distances)
    
    return np.max(distances)
    

# compare the actual contours
def compare_cntrs(num_of_tools, num_pts_cntrs_src, perimeter_cntrs_src, area_cntrs_src, cntrs_src, num_pts_cntrs_dst, perimeter_cntrs_dst, area_cntrs_dst, cntrs_dst):
    diff_cntrs = []
    for i in range(0,num_of_tools):        
        # find closest point on other contour and take the max such distance and do this vice-versa
        # compare cntrs_src[i] and cntrs_dst[i]; same contours might be ordered differently in the two sets
        # so we will need a slightly more complicated way of checking; we ignore this for now and assume that
        # cntrs_src[i] is associated with cntrs_dst[i]
        diff_cntrs.append(np.asarray([perimeter_cntrs_dst[i] - perimeter_cntrs_src[i], (area_cntrs_dst[i] - area_cntrs_src[i])/area_cntrs_src[i], get_dist(cntrs_src[i], cntrs_dst[i]), get_dist(cntrs_dst[i], cntrs_src[i])], dtype = np.float32))        
        
    return diff_cntrs
            

if __name__ == '__main__':
    for fname in os.listdir(folder_txt_src):        
        if fname.endswith(".txt") or fname.endswith(".TXT"): 
            # first get the src file details
            print(fname)            
            errorStr = "" # stores the errors and prints to file if not empty             
            filename = os.path.splitext(fname)[0]
            sh_img_src, bkgrnd_box_src, num_of_tools_src, offsetX_src, offsetY_src, num_pts_convexhull_src, convexhulls_src, num_pts_cntrs_src, perimeter_cntrs_src, area_cntrs_src, cntrs_src = get_content(filename, folder_txt_src)            
            sh_img_dst, bkgrnd_box_dst, num_of_tools_dst, offsetX_dst, offsetY_dst, num_pts_convexhull_dst, convexhulls_dst, num_pts_cntrs_dst, perimeter_cntrs_dst, area_cntrs_dst, cntrs_dst = get_content(filename, folder_txt_dst)
    
            # compare the image sizes
            diff_sh_img = compare_sh_img(sh_img_src, sh_img_dst)            
            # compare the bkgrnd boxes
            diff_bkgrnd_box = compare_bkgrnd_box(bkgrnd_box_src, bkgrnd_box_dst)  
            if (np.max(diff_bkgrnd_box) > THRESH_BKGRND_BOX):
                print('bkgrnd_box does not match!')
                errorStr += np.array2string(diff_bkgrnd_box) + " # bkgrnd_box does not match \n" 
                
            # compare the num of tools
            diff_num_of_tools = compare_num_of_tools(num_of_tools_src, num_of_tools_dst)              
            if np.abs(diff_num_of_tools) > 0:
                print('number of contours does not match!')
                errorStr += np.array2string(np.abs(diff_num_of_tools)) + " # number of contours does not match \n"
            if np.abs(diff_num_of_tools) == 0:
                # compare the offsets
                diff_offset = compare_offset(num_of_tools_src, offsetX_src, offsetY_src, offsetX_dst, offsetY_dst)     
                for i in range(0,num_of_tools_src):                
                    if np.abs(diff_offset[i][0]) > THRESH_OFFSETX or np.abs(diff_offset[i][1]) > THRESH_OFFSETY:
                        print('offset does not match for tool %d ' %(i))
                        errorStr += np.array2string(np.asarray([offsetX_src[i], offsetY_src[i], offsetX_dst[i], offsetY_dst[i]]))
                        errorStr += " # offset does not match for tool " + str(i) + " \n"
                        
                # compare the convex hulls
                diff_convexhulls = compare_convexhulls(num_of_tools_src, num_pts_convexhull_src, convexhulls_src, num_pts_convexhull_dst, convexhulls_dst)                
                for i in range(0,num_of_tools_src):                     
                    if np.abs(diff_convexhulls[i][0]) > THRESH_PERIMETER or np.abs(diff_convexhulls[i][1]) > THRESH_AREA or  np.abs(diff_convexhulls[i][2]) > THRESH_DIST or np.abs(diff_convexhulls[i][3]) > THRESH_DIST:
                        print('convexhull does not match for tool %d ' %(i))
                        errorStr += np.array2string(diff_convexhulls[i])
                        errorStr += " # convexhull does not match for tool " + str(i) + " \n"
                
                #print(diff_convexhulls)
                # compare the actual contours
                diff_cntrs = compare_cntrs(num_of_tools_src, num_pts_cntrs_src, perimeter_cntrs_src, area_cntrs_src, cntrs_src, num_pts_cntrs_dst, perimeter_cntrs_dst, area_cntrs_dst, cntrs_dst)
                for i in range(0,num_of_tools_src):                     
                    if np.abs(diff_cntrs[i][0]) > THRESH_PERIMETER or np.abs(diff_cntrs[i][1]) > THRESH_AREA or  np.abs(diff_cntrs[i][2]) > THRESH_DIST or np.abs(diff_cntrs[i][3]) > THRESH_DIST:
                        print('contour does not match for tool %d ' %(i))
                        errorStr += np.array2string(diff_cntrs[i])
                        errorStr += " # contour does not match for tool " + str(i) + " \n"
                #print(diff_cntrs)
        
            if len(errorStr) > 0:
                print('Found errors...writing to file')
                # open error file to write the string
                fileError = open(os.path.join(folder_txt_err,filename+".txt"),'w')
                fileError.write(errorStr)
                fileError.close()
#                
        
                