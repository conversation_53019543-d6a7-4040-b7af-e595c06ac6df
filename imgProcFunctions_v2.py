# -*- coding: utf-8 -*-
"""
Created on Mon Mar  2 13:23:04 2020

@author: SK & RU
"""

import numpy as np
import cv2
import imutils
import os
import matplotlib.pyplot as plt
import itertools
import sys
import math

MYENVIRONMENT = 'HEROKU' # for pushing onto heroku<PERSON>ser and deploying on heroku
MYENVIRONMENT = 'PYTHON' # delete for heroku # set LOCAL to run things on locolhost; set PY<PERSON><PERSON> to run things with dropbox data; set CRAWL to process imgs grabbed from web
TESTING = False # testing for .txt output with image size; background paper size; offsetx; offsety; convex hull data; raw path data
DEBUG = False # for detail debugging of one example with pictures
SIGMA = 0.33 # for Canny edges detection
THRESH_CONTOUR = 50 # for removing really small contours
USE_SEGMENTATION = True # segmentation or edge detection
USE_SEGMENTATION_TOOL_OUTLINE = False # use segmentation also for tool outline detection
USE_DOWNSAMPLED_IMAGE = True # use the downsampled image before perspectvive transformation for tool edge detection
THRESH_CONTOURAREA_BOXAREA = 0.01 # for removing contours which have contour area much smaller than the bounding box
USE_HIGH_RES = True # use the original high resolution image (3024x4024)

# size of A4 paper in mm
A4_h = 297
A4_w = 210

# how many px make one mm on the screen
pxpermm = 96/25.4

# error message to be sent back -- background paper clipped; floor contrast etc. 
ERROR_MESSAGE = "" # default is empty -- no error
ERROR_THREAT = "" # default is empty -- no threat; low = color orange; high = color red

# perimeter length of the contour
def cntrperi(cnt):
   return cv2.arcLength(cnt,True)

# area of the contour
def cntrarea(cnt):
    return cv2.contourArea(cnt)

# get the area of a rectangular box
def get_area(box):
    h = np.linalg.norm(box[1]-box[2])
    w = np.linalg.norm(box[2]-box[3])
    
    return h*w

# Finds the intersection of two lines given in Hesse normal form. Returns closest integer pixel locations.
# See https://stackoverflow.com/a/383527/5087436
def intersection(line1, line2):

    rho1 = line1[0]; theta1 = line1[1]
    rho2 = line2[0]; theta2 = line2[1]
    A = np.array([
        [np.cos(theta1), np.sin(theta1)],
        [np.cos(theta2), np.sin(theta2)]
    ])        
    b = np.array([[rho1], [rho2]])
    #print(A,b,np.linalg.det(A))
    x0, y0 = np.linalg.solve(A, b)
    x0, y0 = int(np.round(x0)), int(np.round(y0))
    return np.array([x0, y0],dtype=np.int32)


def plot_lines(image, pruned_lines):
    #Display on the original image
    sh = image.shape
    for i in range(0,len(pruned_lines)):
       rho = pruned_lines[i][0]
       theta = pruned_lines[i][1]
       a = np.cos(theta)
       b = np.sin(theta)
         
       x0 = a*rho
       y0 = b*rho
       x1 = int(x0 + np.maximum(sh[0],sh[1])*(-b))
       y1 = int(y0 + np.maximum(sh[0],sh[1])*(a))
       x2 = int(x0 - np.maximum(sh[0],sh[1])*(-b))
       y2 = int(y0 - np.maximum(sh[0],sh[1])*(a))
       # overlay line on original image
       cv2.line(image,(x1,y1),(x2,y2),(0,255,0),2)

# check if line l is "contained" in the set of lines
def is_contained_in_lines_seg(myl, mylines, sh_img, TOLERANCE):
    
    if len(mylines) == 0:
        return False    
    
    # first make sure things are ok when rho is negative
    if myl[0] < 0:        
        myl[1] = myl[1] - np.pi             
        myl[0] = np.abs(myl[0])
            
    for i in range(0,len(mylines)):
        if mylines[i][0] < 0:
            mylines[i][0] = np.abs(mylines[i][0])
            mylines[i][1] = mylines[i][1] - np.pi
            
    temp_lines = np.abs(mylines - myl)    
    if (np.min(np.abs(temp_lines[:,0]/np.maximum(sh_img[0],sh_img[1])) + np.abs(temp_lines[:,1]/np.pi))) <= TOLERANCE:
        return True
    else:
        return False


# perspective transform: we have four corners of the 640x480 rectangle as the 
# destination and the bkgrnd_box as the source (four) corner points
# use getPerspectiveTransform to get the projection from src -> dst.
# In this projective transform, straight lines remain as straight lines,
# consequently we also get the cropped image lying within the bkgrnd_box duly transformed
def get_perspective_transform(bkgrnd_box, sh_dst, img, gray):                  

    global ERROR_MESSAGE
    global ERROR_THREAT
    
    # 640 rows x 480 cols are arranged in anti-clockwise order
    pts2 = np.float32([[0,0], [0,sh_dst[0]], [sh_dst[1],sh_dst[0]], [sh_dst[1],0]])
    pts1 = -1*np.ones(np.shape(pts2))
   
    # find the long edge of the bkgrnd_box
    if (np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][1]) >= np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][2])):
        # edge 0-1 is larger than edge 1-2        
        pts1[0,:] = bkgrnd_box[0][0]
        pts1[1,:] = bkgrnd_box[0][1]
        pts1[2,:] = bkgrnd_box[0][2]
        pts1[3,:] = bkgrnd_box[0][3]
        pts1 = np.float32(pts1) 
        M = cv2.getPerspectiveTransform(pts1,pts2)
        if (np.linalg.det(M) < 0):
            # change orientation
            pts1[0,:] = bkgrnd_box[0][1]
            pts1[1,:] = bkgrnd_box[0][0]
            pts1[2,:] = bkgrnd_box[0][3]
            pts1[3,:] = bkgrnd_box[0][2]
            pts1 = np.float32(pts1) 
    else:        
        # edge 0-1 is smaller than edge 1-2
        pts1[0,:] = bkgrnd_box[0][1]
        pts1[1,:] = bkgrnd_box[0][2]
        pts1[2,:] = bkgrnd_box[0][3]
        pts1[3,:] = bkgrnd_box[0][0]
        pts1 = np.float32(pts1) 
        M = cv2.getPerspectiveTransform(pts1,pts2)
        if (np.linalg.det(M) < 0):
            # change orientation
            pts1[0,:] = bkgrnd_box[0][2]
            pts1[1,:] = bkgrnd_box[0][1]
            pts1[2,:] = bkgrnd_box[0][0]
            pts1[3,:] = bkgrnd_box[0][3]     
            pts1 = np.float32(pts1)             
      
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pts1:= ', pts1)
        print('pts2:= ', pts2)
            
    M = cv2.getPerspectiveTransform(pts1,pts2)
    if (np.linalg.det(M) < 0):
        print('Perspective transform: there are still flips!!!')
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('perspective transform matrix:= ', M, np.linalg.det(M))
            
    gray_new = cv2.warpPerspective(gray,M,(sh_dst[1],sh_dst[0]))
    img_new = cv2.warpPerspective(img,M,(sh_dst[1],sh_dst[0]))
    
    return img_new, gray_new

# perspective transform: if we find that perspective transform has flipped things, unflip by 
# doing either vertical OR horizontal flip. This should make product of flips = 1
def get_perspective_transform_with_flip(bkgrnd_box, sh_dst, img, gray):                  

    # 640 rows x 480 cols are arranged in anti-clockwise order
    pts2 = np.float32([[0,0], [0,sh_dst[0]], [sh_dst[1],sh_dst[0]], [sh_dst[1],0]])
    pts1 = -1*np.ones(np.shape(pts2))
   
    # find the long edge of the bkgrnd_box
    if (np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][1]) >= np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][2])):
        # edge 0-1 is larger than edge 1-2        
        pts1[0,:] = bkgrnd_box[0][0]
        pts1[1,:] = bkgrnd_box[0][1]
        pts1[2,:] = bkgrnd_box[0][2]
        pts1[3,:] = bkgrnd_box[0][3]        
    else:        
        # edge 0-1 is smaller than edge 1-2
        pts1[0,:] = bkgrnd_box[0][1]
        pts1[1,:] = bkgrnd_box[0][2]
        pts1[2,:] = bkgrnd_box[0][3]
        pts1[3,:] = bkgrnd_box[0][0]        
      
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pts1:= ', pts1)
        print('pts2:= ', pts2)

    pts1 = np.float32(pts1)             
    M = cv2.getPerspectiveTransform(pts1,pts2)
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('perspective transform matrix:= ', M, np.linalg.det(M))

    gray_new = cv2.warpPerspective(gray,M,(sh_dst[1],sh_dst[0]))
    img_new = cv2.warpPerspective(img,M,(sh_dst[1],sh_dst[0]))
    
    if (np.linalg.det(M) < 0):
        print('Perspective transform: there is a flip...unflipping!!!')
        # any flip would unflip things; so do a flip along y-axis
        img_new = cv2.flip(img_new, 0)
        gray_new = cv2.flip(gray_new, 0)
    
    return img_new, gray_new


# for images where object has a little part touching the background paper 
# we cleanup the boundary by setting a small sliver at each side to 0
def clean_up_boundary(im):
    # just make a few pixels around the boundary black
    THRESH_BNDRY = 2 
    im[0:THRESH_BNDRY, :] = 0
    im[im.shape[0]-THRESH_BNDRY:im.shape[0], :] = 0
    im[:,0:THRESH_BNDRY] = 0
    im[:,im.shape[1]-THRESH_BNDRY:im.shape[1]] = 0
    
    return im


# check closeness of boxes of max_box and box
def check_closeness(max_hull, hull):
    
    # tolerance to determine closeness
    TOLERANCE_CLOSE = -3
    flag = False     
    
    # pointPolygonTest gives the shortest signed distance between a point and a contour
    # if distance is negative it is outside, if positive then inside, if 0 then on the contour
    # check if any point in the hull is inside max_hull by at least TOLERANCE_CLOSE    
    for pt in hull:        
        dist = cv2.pointPolygonTest(np.asarray(max_hull),tuple(np.squeeze(pt)),True)
        if dist >= TOLERANCE_CLOSE:
            flag = True
    # check if any point in the max_hull is inside hull by at least TOLERANCE_CLOSE
    for pt in max_hull:
        dist = cv2.pointPolygonTest(np.asarray(hull),tuple(np.squeeze(pt)),True)
        if dist >= TOLERANCE_CLOSE:
            flag = True    

    return flag


# check if any other contour can be merged with the max_cntr
def merge_cntrs(max_cntr, other_cntrs):
    
    # find box/rect around the max_cntr call it max_box    
    max_hull = cv2.convexHull(max_cntr)      
      
    # go over the other_cntrs and check if any rect/box around that cntr is close to the max_cntr
    # if "close" then merge it with the max_cntr
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('other_cntrs',len(other_cntrs))
    for c in other_cntrs:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('candidate cntr for merging: ', len(c))        
        hull = cv2.convexHull(c)             
        # check if one corner of the box is close to any one of the corners of the max_box          
        #if (check_closeness(max_box, box, sh) and (get_area(box) <= 0.4*get_area(max_box))):
        if (check_closeness(max_hull, hull)):
        #if (check_closeness(max_box, box, sh) and (cntrarea(c) <= 0.5*cntrarea(max_cntr))):
            # boxes are close and box is "smaller" compared to max_box and we now merge them
            max_cntr = np.concatenate((max_cntr,c),axis=0)   
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                print('cntr was merged with max_cntr')
    return max_cntr


# return the max contour possibly merged with neighboring contours
def get_pruned_cntrs(image,image_orig):
        
    img_copy = np.copy(image_orig)    
    # get contours around the objects
    major = cv2.__version__.split('.')[0]
    if major == '3':
        im2, cnts, hier = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        cnts, hier = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrperi, reverse = True)[:20] #sort the top 20
    
    max_cntr = []
    other_cntrs = [] # list of all cntrs which are not the max_cntr
    max_area = 0
    for c in cnts:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON': 
            rect = cv2.minAreaRect(c)            
            box = cv2.boxPoints(rect)
            box = np.int0(box) 
            cv2.drawContours(img_copy, [c], 0, (0,255,0), 4) 
            #print(cntrperi(c), cntrarea(c), box)
            cv2.drawContours(img_copy, [box], 0, (0,0,255), 4)
    	# if the contour has really small perimeter, discard it
        if cntrperi(c) > THRESH_CONTOUR:                       
            rect = cv2.minAreaRect(c)            
            box = cv2.boxPoints(rect)
            box = np.int0(box)                  
            area = cntrarea(c)
            #if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            #    print('area', area, 'first_pt', c[0], 'last_pt', c[len(c)-1], 'box', box)
            if (area >= max_area):                
                # found a contour of larger size
                # put the previous max_cntr, if not empty, into other_cntrs
                if (len(max_cntr)>0):
                    other_cntrs.append(max_cntr)
                max_area = area
                max_cntr = c
            else:
                if (len(max_cntr)>0):
                    other_cntrs.append(c)
                
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':        
        print('number of other cntrs: ',len(other_cntrs))
        for c in other_cntrs:
            print('first point in other cntr: ', c[0])
        print('max_cntr length: ',len(np.squeeze(max_cntr))) 
        if len(max_cntr) > 0:
            print('first point in max_cntr: ', max_cntr[0])
        plt.figure();plt.imshow(img_copy, cmap='gray')
    
    # check if any from the other_cntrs can be merged with the max_cntr
    if (len(other_cntrs) > 0):
        max_cntr = merge_cntrs(max_cntr, other_cntrs)
    
    return max_cntr


# check if intersection of two lines lies outside the image
def intersectWithTwoLines(myline, mylistoflines, sh):    
    count_intersection = 0
    for line in mylistoflines:    
        # ignore lines which have theta very close to each other
        if (np.abs(myline[1]-line[1])) > 1e-4: 
            # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
            if (np.abs(1/np.tan(myline[1])-1/np.tan(line[1]))) > 1e-4:                 
                a = intersection(myline, line)                
                # count the number of intersections inside the image
                if (a[0] < sh[1] and a[0] > 0 and a[1] > 0 and a[1] < sh[0]):            
                    count_intersection += 1        
        
    return count_intersection
            

# get the largest contour in terms of area enclosed
def get_largest_cntr(pruned_cntrs):
        
    max_area = cntrarea(pruned_cntrs[0])
    if len(pruned_cntrs) == 1:
        return pruned_cntrs
    else:
        for c in pruned_cntrs:
            if cntrarea(c) >= max_area:
                largest_cntr = c
                max_area = cntrarea(c)
                #print(max_area)
    return [largest_cntr]

# get Hough lines from the edge detection image
def get_lines_edge_image(gray, blur_size, hough_threshold, sh_img):
    # Gaussian smoothing to remove spurious edges
    blur = cv2.GaussianBlur(gray,(blur_size,blur_size),0) 
    # compute the median of the single channel pixel intensities
    v = np.median(blur)
    # apply automatic Canny edge detection using the computed median
    # use SIGMA = 0.33 here 
    lower = int(max(0, (1.0 - SIGMA) * v))
    upper = int(min(255, (1.0 + SIGMA) * v))
    #lower = 100
    #upper = 200
    edges = cv2.Canny(blur,lower,upper)    
    # "closing" transformation        
    # kernel for "closing" transformation 
    kernel_close = np.ones((7,7),np.uint8)
    img_for_bkgrnd_edge = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)
    
    image = img_for_bkgrnd_edge
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('blur_size %d hough_threshold %d lower %d, upper %d for bkgrnd edge detection' % (blur_size, hough_threshold, lower, upper))
        figtitle="edge image for bkgrnd box detection blur_size = " + str(blur_size) + " hough_thresh = " + str(hough_threshold)
        plt.figure(figtitle);plt.imshow(img_for_bkgrnd_edge, cmap='gray')
    
    # get the Hough lines on the edge image
    lines_edge = cv2.HoughLines(image,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/hough_threshold)) 
    if lines_edge is not None:
        lines_edge = np.squeeze(lines_edge)
    
    return lines_edge

# check if there are 4 distinct lines that we want or not (this is mostly when we have around <50 lines)
def get_num_distinct_lines(lines_edge):
    
    num = len(lines_edge) # starting number of distinct lines
    distinct_lines = np.ones(num,dtype=int) # all are distinct at the start
    #print(distinct_lines)
    for i in range(0,num):        
        if lines_edge[i][0] < 0:            
            thetai = np.pi - lines_edge[i][1]
        else:
            thetai = lines_edge[i][1]
        for j in range(i+1,num):
            if distinct_lines[j] == 1:
                flag_slope = False
                if lines_edge[j][0] < 0:            
                    thetaj = np.pi - lines_edge[j][1]
                else:
                    thetaj = lines_edge[j][1]
                # sometimes convention of only positive theta is not followed hence we have second condition below
                if np.abs(thetai-thetaj) < 1e-2 or np.abs(1/np.tan(thetai)-1/np.tan(thetaj)) < 1e-2: 
                    flag_slope = True  
                #print(i,j,np.abs(np.abs(lines_edge[i][0]) - np.abs(lines_edge[j][0])),flag_slope)                                  
                if (np.abs(np.abs(lines_edge[i][0]) - np.abs(lines_edge[j][0])) < 5 and flag_slope == True):
                    distinct_lines[j] = 0
    #print(distinct_lines, len(np.nonzero(distinct_lines)[0]))
    return len(np.nonzero(distinct_lines)[0])
    
# get the background paper from the image
def get_background_paper(gray,img, sh_img):
    
    global ERROR_MESSAGE
    global ERROR_THREAT
    # two ways to get the background paper; currently using the segmentation/thresholding approach
    if USE_SEGMENTATION:
        # simple thresholding for segmentation        
        ret,th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        #img_seg = cv2.bitwise_not(th)        
        # "opening" transformation
        # kernel for "opening" transformation 
        #if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        #    plt.figure();plt.imshow(th, cmap='gray')
        kernel_open = np.ones((7,7),np.uint8)
        img_for_bkgrnd = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            plt.figure("segmentation/thresholding");plt.imshow(img_for_bkgrnd, cmap='gray')
        #opening = cv2.morphologyEx(closing, cv2.MORPH_OPEN, kernel_open)
    else:
        # gaussian blur to remove spurious edges
        blur = cv2.GaussianBlur(gray,(3,3),0) # originally 3 was used
        # compute the median of the single channel pixel intensities
        v = np.median(blur)
        # apply automatic Canny edge detection using the computed median
        lower = int(max(0, (1.0 - SIGMA) * v))
        upper = int(min(255, (1.0 + SIGMA) * v))
        #lower = 100
        #upper = 200
        edges = cv2.Canny(blur,lower,upper)    
        # "closing" transformation        
        # kernel for "closing" transformation 
        kernel_close = np.ones((7,7),np.uint8)
        img_for_bkgrnd = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close) 
        
    image = img_for_bkgrnd
    
    # the paper might not be parallel to the axis of the image 
    # use findContours to get the background paper 
    major = cv2.__version__.split('.')[0]
    if major == '3':       
        im2, cnts, hier = cv2.findContours(image,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        cnts, hier = cv2.findContours(image,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrarea, reverse = True)[:20]
    
    # blank image to draw the big contour which most likely represents the background paper
    img_bl =  np.zeros((sh_img[0],sh_img[1]),dtype = "uint8")
    bkgrnd_box = []
    max_cntr = cnts[0]
                        
    # get the temporary bkgrnd_box and draw the contour on a blank image for further processing
    # epsilon is the max distance from the original contour
    # we keep this to 5% from the arc_length of the original contour
    # this helps get rid of weird artifacts (like a small edge jutting out because of bad floor)
    epsilon = 0.05*cv2.arcLength(max_cntr,True)
    approx = cv2.approxPolyDP(max_cntr,epsilon,True)
    rect = cv2.minAreaRect(approx)
    box = cv2.boxPoints(rect)
    box = np.int0(box) 
                
    # use the corner points of the contour rather than the background box
    # these corner points can be got by the intersection of lines in lines_seg
    bkgrnd_box.append(box) # this bkgrnd_box is used in case of the edge detection process gives < 4 lines
    # use the max_cntr instead of approx contour got above to get the lines_seg because we don't want to lose information
    cv2.drawContours(img_bl,[max_cntr],0,(255,255,255),4)
    
    # ERROR_DETECTION: phone far away from background paper
    # if the area of the max_cntr is much smaller than 640x480
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print("ratio of max_cntr area to total image area:= ", cntrarea(max_cntr)/(img_bl.shape[0]*img_bl.shape[1]))
    
    if cntrarea(max_cntr)/(img_bl.shape[0]*img_bl.shape[1]) <= 0.125:
        ERROR_MESSAGE += "Zoom in? "
        ERROR_THREAT = "red"
        
    # ERROR_DETECTION: Shadow?
    # if the area of the max_cntr is smaller than its bounding box, then perhaps there was shadow cast on the paper
    if cntrarea(max_cntr)/get_area(box) < 0.8:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print("max_cntr area vs its bounding box area: ", cntrarea(max_cntr), get_area(box), cntrarea(max_cntr)/get_area(box))
        ERROR_MESSAGE += "Shadow? "
        ERROR_THREAT = "red"
    
#    # ERROR_DETECTION: phone tilted
#    # if the max_cntr area is smaller than bounding box of that max_cntr then flag skewness
#    rect_max_cntr = cv2.minAreaRect(approx)
#    box_max_cntr = cv2.boxPoints(rect_max_cntr)
#    box_max_cntr = np.int0(box_max_cntr) 
#    # if the max_cntr area is smaller than its bounding box, declare skewness
#    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
#        print("AREAS RATIO:= ", cntrarea(approx)/get_area(box_max_cntr))
#    if cntrarea(approx)/get_area(box_max_cntr) < 0.85:
#        ERROR_MESSAGE += "Phone tilted?"
#        ERROR_THREAT = "red"
#        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
#            print("AREAS RATIO:= ", cntrarea(max_cntr)/get_area(box_max_cntr))
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("background box obtained from segmentation");plt.imshow(img_bl, cmap='gray')
    
    # use Hough lines on the binary image with only the contour
    lines_seg = cv2.HoughLines(img_bl,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/3.0))    
    if lines_seg is not None:        
        lines_seg = np.squeeze(lines_seg)
        
    if DEBUG == True and MYENVIRONMENT == 'PYTHON': 
        if lines_seg is not None:           
            img_copy = np.copy(img)
            plot_lines(img_copy,lines_seg)
            plt.figure("hough lines obtained on previous black and white image");plt.imshow(img_copy, cmap='gray')        
            img_copy = np.copy(img)
            cv2.drawContours(img_copy, bkgrnd_box, -1, (255,0,255), 2)
            plt.figure("background box obtained from segmentation on original image");plt.imshow(img_copy, cmap='gray')
        
    # bkgrnd box from segmentation process for backup
    bkgrnd_box_seg = np.copy(bkgrnd_box)
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('bkgrnd_box_seg (backup): ', bkgrnd_box_seg)
    
    ##############################################################################################
    ################### Get Hough lines from the Edge detection process ##########################
    ##############################################################################################
    
    # get the Hough lines for the edge detected image
    blur_size = [1,3,5,7]
    hough_threshold = [4,5,6]
    # use blur_size = 3 and hough_threshold = 4 to start with
    lines_edge = get_lines_edge_image(gray, blur_size[1], hough_threshold[0], sh_img)    
    # sometimes even if we get > 4 lines, they need not be the ones around the paper;
    # it could just be clustering of multiple lines aroudn one line
    # in this case, we need to check first that we have existence of 4 lines around paper
    # if not then we need to reduce blur and/or decrease hough_threshold    
    if lines_edge is not None:            
        distinct_lines = len(lines_edge)
    else:
        distinct_lines = 0
        
    if distinct_lines > 4 and distinct_lines < 50: # if num of lines > 50, then perhaps we assume that we have those lines we want
        distinct_lines = get_num_distinct_lines(lines_edge)
    
    # sometimes background color is close to white paper, then we need to decrease blur to 1 
    # and reduce Hough threshold to sh_img[1]/5 to make sure we get our desired lines  
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        #print(lines_edge)
        print('distinct lines %d' % distinct_lines)
    if distinct_lines < 4:
        # make blur_size = 1        
        for j in range(0,len(hough_threshold)):
            lines_edge = get_lines_edge_image(gray, blur_size[0], hough_threshold[j], sh_img)
            if lines_edge is not None:
                distinct_lines = len(lines_edge)
            else:
                distinct_lines = 0
            if distinct_lines > 4 and distinct_lines < 50: # if num of lines > 50, then perhaps we assume that we have those lines we want
                distinct_lines = get_num_distinct_lines(lines_edge)
            if distinct_lines >= 4:
                break

    # sometimes because of carpet or hardwood or tiles we might get thousands of undesired lines in the edge detection
    # because blurring isn't too large. In such cases when num of lines_edge is > 1000 
    # increase the blurring to first 5, if it is still large > 1000, increase it to 7. 
    # we keep the hough_threshold fixed to 4 so that we are being strict with the lines we want
    elif distinct_lines >= 1000:        
        for i in range(2,len(blur_size)):
            lines_edge = get_lines_edge_image(gray, blur_size[i], hough_threshold[0], sh_img)
            if lines_edge is not None:
                number_lines_edge = len(lines_edge)
            else:
                number_lines_edge = 0
            if number_lines_edge < 1000:
                break            
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        img_copy = np.copy(img)
        #print(lines_edge)
        if lines_edge is not None:
            plot_lines(img_copy,lines_edge)
            print('number of lines_edge: %d' % len(lines_edge))
            plt.figure("hough lines on edge-detection image");plt.imshow(img_copy, cmap='gray')    
        else:
            print('lines_edge is EMPTY, perhaps no contrast with floor!')
        #print('lines from segmentation')
        #print(lines_seg)        
        #print('lines from edge detection')
        #print(lines_edge)
    
    # get the intersection of Hough lines obtained by the two methods above: lines_seg and lines_edge
    lines = []
    if lines_seg is not None and lines_edge is not None:
        line_count = 0        
        for l in lines_edge:
            # check if we have a new line_edge        
            if is_contained_in_lines_seg(l, lines_seg, sh_img, 0.02) and not (is_contained_in_lines_seg(l, lines, sh_img, 0.1)): 
                lines.append(l)
                line_count += 1
        
    # convert to list of numbers
    if (len(lines) > 0):
        lines = np.asarray(lines,dtype=np.float32)
    else:
        line_count = 0
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('intersected lines')
        print(lines)
        img_copy = np.copy(img)
        plot_lines(img_copy,lines)
        plt.figure("intersection of lines_seg and lines_edge");plt.imshow(img_copy, cmap='gray')
        
    if line_count < 4:
        print('Some sing is wong!!! Less than four lines (%d) found!!! Using the bkgrnd_box found in segmentation!' % line_count)
        # ERROR_DETECTION: Background paper properly detected?
        # check if the hough line detection gave exactly 4 lines; if not then background paper was not detected properly and 
        # fall back upon an older background box
        ERROR_MESSAGE += "Background paper properly detected? "
        ERROR_THREAT = "red"        
        bkgrnd_box = bkgrnd_box_seg
        # we found just one line in the intersection, then perhaps the photo is blurry or there is no contrast with the floor        
        if line_count <= 1:
             ERROR_MESSAGE += "No contrast with floor/table or blurry Image? "
        else:
             # lines are still < 4, but check whether bkgrnd_box from segmentation is out of the image
             a = np.min(bkgrnd_box[0], axis=0)
             b = np.max(bkgrnd_box[0], axis=0)
             xmin = a[0]
             ymin = a[1]
             xmax = b[0]
             ymax = b[1]
             # if any is close to the boundary, signal error
             distance = np.min([xmin,ymin,sh_img[1]-xmax,sh_img[0]-ymax])             
             # assume paper is 80% of the image?
             if distance < 0.8*(640/297)*5:
                 ERROR_MESSAGE += "Background paper out of image? "
             
    elif line_count > 4:        
        bkgrnd_box = []
        # check if we can refine to get the four lines of the background paper        
        # get the list of indices for lines
        lines_ind = list(range(0,len(lines)))
        # now go through all the 4 combinations
        candidate_rects = []
        candidate_areas = []
        for subset in itertools.combinations(lines_ind, 4):
            flag = True
            #print(subset)
            # set the flag for the "true" four lines to be True at the start
            # get the four lines
            line1 = lines[subset[0]]; line2 = lines[subset[1]]; line3 = lines[subset[2]]; line4 = lines[subset[3]]
            # check if each line intersects with only two other lines inside the image
            a = intersectWithTwoLines(line1, [line2, line3, line4], sh_img)
            b = intersectWithTwoLines(line2, [line1, line3, line4], sh_img)
            c = intersectWithTwoLines(line3, [line1, line2, line4], sh_img)
            d = intersectWithTwoLines(line4, [line1, line2, line3], sh_img)
            #print("ABCD", a,b,c,d)
            if a!=2 or b!=2 or c!=2 or d!=2: # check if there are two intersection points inside the image
                flag = False                
            # if lines are valid, then compute the area
            if(flag == True):
                # we have perhaps a valid set of four lines
                # find the points of intersection and compute the area
                temp_rect = []
                for i in range(0,len(subset)-1):         
                    for j in range(i+1,len(subset)):
                        line1 = lines[subset[i]]; line2 = lines[subset[j]] 
                        # make sure they aren't parallel lines
                        if (np.abs(line1[1]-line2[1])) > 1e-4: 
                            # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
                            if (np.abs(1/np.tan(line1[1])-1/np.tan(line2[1]))) > 1e-4: 
                                a = intersection(line1, line2)    
                                if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):                         
                                    temp_rect.append(a)
                temp_rect = np.asarray(temp_rect,dtype=np.int64)
                tr = []; tr.append(temp_rect); temp_rect = tr
                # order the corners in bkgrnd_box
                if (np.linalg.norm(temp_rect[0][0] - temp_rect[0][1]) >= np.linalg.norm(temp_rect[0][0] - temp_rect[0][2])):
                    temp_pt = np.copy(temp_rect[0][1])
                    temp_rect[0][1] = temp_rect[0][2]
                    temp_rect[0][2] = temp_pt
                if (np.linalg.norm(temp_rect[0][1] - temp_rect[0][2]) >= np.linalg.norm(temp_rect[0][1] - temp_rect[0][3])):
                    temp_pt = np.copy(temp_rect[0][2])
                    temp_rect[0][2] = temp_rect[0][3]
                    temp_rect[0][3] = temp_pt
                # take the area of the candidate rectangle
                area1 = np.abs(np.cross(temp_rect[0][0] - temp_rect[0][1], temp_rect[0][0] - temp_rect[0][3]))/2.0 
                area2 = np.abs(np.cross(temp_rect[0][2] - temp_rect[0][1], temp_rect[0][2] - temp_rect[0][3]))/2.0 
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print(subset, area1+area2, temp_rect)
                candidate_rects.append(temp_rect)
                candidate_areas.append(area1+area2)
        # choose the largest area 
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print(candidate_areas, len(candidate_areas))
            print(candidate_rects, len(candidate_rects))
        # make sure there is at least one canditate rectangle, then sort and pick the largest rectangle
        if len(candidate_rects) > 0:
            a = sorted(range(len(candidate_areas)), key=lambda k: candidate_areas[k])
            bkgrnd_box = candidate_rects[a[len(a)-1]]
        else:
            # ERROR_DETECTION: Background paper out of image?
            # check if any corner lies outside the image boundary
            ERROR_MESSAGE += "Background paper out of image? "
            ERROR_THREAT = "red"
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('bkgrnd_box when > 4 lines')
            print(bkgrnd_box)
    else:         
        bkgrnd_box = []     
        for i in range(0,len(lines)-1):         
            for j in range(i+1,len(lines)):
                line1 = lines[i]; line2 = lines[j]
                # make sure they aren't parallel lines
                if np.abs(line1[1] - line2[1]) > 1e-4: 
                    # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
                    if (np.abs(1/np.tan(line1[1])-1/np.tan(line2[1]))) > 1e-4: 
                        a = intersection(line1, line2)
                        #print('pts of intersection: ', a)
                        if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):
                            bkgrnd_box.append(a)    
        #print('bkgrnd_box', bkgrnd_box)
        # convert to list of numbers
        if (len(bkgrnd_box) > 0):
            a = np.asarray(bkgrnd_box,dtype=np.int64)
            b = []
            b.append(a)
            bkgrnd_box = b     
            
        # note that in the above we take intersection of the 4 lines
        # this might result in a corner point lying outside the image 
        # because originally the paper was outside the image view
        # so we have to take care of the fact that we might get only <= 3 corner points

        # order the corners in bkgrnd_box only if there are 4 sides
        # if not then it is taken care of later 
        if len(bkgrnd_box) > 0:
            if len(bkgrnd_box[0]) == 4:            
                if (np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][1]) >= np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][2])):
                    temp_pt = np.copy(bkgrnd_box[0][1])
                    bkgrnd_box[0][1] = bkgrnd_box[0][2]
                    bkgrnd_box[0][2] = temp_pt
                if (np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][2]) >= np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][3])):
                    temp_pt = np.copy(bkgrnd_box[0][2])
                    bkgrnd_box[0][2] = bkgrnd_box[0][3]
                    bkgrnd_box[0][3] = temp_pt  
            elif len(bkgrnd_box[0]) < 4:
                # ERROR_DETECTION: Background paper out of image?
                # check if any corner lies outside the image boundary
                ERROR_MESSAGE += "Background paper out of image? "
                ERROR_THREAT = "red"
        else:
            # we couldn't find background paper
            ERROR_MESSAGE += "Background paper out of image? "
            ERROR_THREAT = "red"
        
    if len(bkgrnd_box) > 0: # make sure there are 4 corners
        if len(bkgrnd_box[0]) < 4:                                 
            # if bkgrnd_box doesn't have corners then revert back to the one obtained from segmentation if it has four corners
            if len(bkgrnd_box_seg) > 0:
                if len(bkgrnd_box_seg[0]) == 4:    
                    bkgrnd_box = bkgrnd_box_seg
                else:
                    print('No background paper found anywhere :- 1 !!!')
                    # just use the entire 640x480 as background box in this case
                    bkgrnd_box = []
                    bkgrnd_box.append(np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]]))
            else:
                print('No background paper found anywhere :- 2 !!!')
                # just use the entire 640x480 as background box in this case
                bkgrnd_box = []
                bkgrnd_box.append(np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]]))
        else:
            # just check if we have signalled ERROR_MESSAGE = Shadow?. This might have happened because the segmentation image
            # had some defects due to the floor or something else and Shadows was not really the problem at this junture 
            # because we have found 4 lines in the intersection of lines_seg and lines_edge. So perhaps Shadow? is wrongly signalled
            # we just remove it
            if ERROR_MESSAGE.find("Shadow") != -1 and line_count >= 4:                        
                ERROR_MESSAGE = ERROR_MESSAGE.replace("Shadow? ", "") 
                if ERROR_MESSAGE == "":
                    ERROR_THREAT = ""
    else: # if bkgrnd_box obtained from intersection of edge and segmented image gave nothing then revert to the previous one
        if len(bkgrnd_box_seg) > 0:            
            if len(bkgrnd_box_seg[0]) == 4:    
                bkgrnd_box = bkgrnd_box_seg                
            else:
                print('No background paper found anywhere :-3 !!!')
                # just use the entire 640x480 as background box in this case
                bkgrnd_box = []
                bkgrnd_box.append(np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]]))
        else:
            print('No background paper found anywhere :-4 !!!')
            # just use the entire 640x480 as background box in this case
            bkgrnd_box = []
            bkgrnd_box.append(np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]]))
        
    return bkgrnd_box

# get the distance of the contour from the image boundary
def distance_of_contour_to_boundary(cntr, sh_img):    
    cntr = np.squeeze(cntr)     
    #print('max of cntr is:= ', a[0],a[1])
    xymax = np.amax(cntr,axis=0)
    xmax = xymax[0]
    ymax = xymax[1]
    xymin = np.amin(cntr,axis=0)
    xmin = xymin[0]
    ymin = xymin[1]    
    distance = np.min([xmin,ymin,sh_img[1]-xmax,sh_img[0]-ymax])
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print("xmin, xmax, ymin, ymax, mindist ", xmin, xmax, ymin, ymax, distance)
    return distance

# return things to the javascript tool
def data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, flag_scale):

    global ERROR_MESSAGE
    global ERROR_THREAT
    
    final_number_of_tools = 0
    
    if len(pruned_cntrs) == 0:
        print('Some sing is wong!!! Zero contours found!!!')
        # return empty sets/numbers
        ERROR_MESSAGE += "Photo too blurry or tool has no contrast with paper? "
        return [],[],[],-1,-1
    else:        
        
        myhulls = []
        myrawdata = []
        myoffsetX = []
        myoffsetY = []   
        myprunedCntrs= []
        for c in pruned_cntrs: 
            rect = cv2.minAreaRect(c)            
            box = cv2.boxPoints(rect)
            box = np.int0(box)
            # if the outline is such that its contour area is much smaller than the bounding box area
            # then most likely this is a long spurious edge and we should just throw it away         
            # OR is contour area is less than 25mm^2 then throw it away (assume 1 mm = 640/297 pixels)
            if (cntrarea(c)/get_area(box) < THRESH_CONTOURAREA_BOXAREA) or cntrarea(c) < (640/297*5)*(640/297*5):
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':                    
                    print('eliminated one contour because either its contour area is much smaller than the bounding box area OR itself has small area! ', cntrarea(c), get_area(box))
                continue
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                print('area of contour, perimeter of contour, area of contour/area of bounding box ', cntrarea(c), cntrperi(c), cntrarea(c)/get_area(box))
            
            # count number of tools
            final_number_of_tools += 1
                                    
            # get the convex hull
            myprunedCntrs.append(c)
            hull = cv2.convexHull(c)              
                        
            # ERROR_DETECTION: Tool too close or extends beyond paper boundary? 
            # Check: If the closest point of any tool path is closer than 1mm from the white paper boundary.
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                print("tool distance from boundary:= ", distance_of_contour_to_boundary(c, sh_img))
            if distance_of_contour_to_boundary(c, sh_img) <= np.ceil((640/297)*1):                
                if ERROR_MESSAGE.find("Tool too close to paper boundary or extends beyond it") == -1:
                    ERROR_MESSAGE += "Tool too close to paper boundary or extends beyond it? "                    
                ERROR_THREAT = "red"
                # check if we had an ERROR_MESSAGE = Shadow?. Because perhaps we signalled it wrongly because tool
                # was too large and previous criteria signalled Shadow?. 
                # If the tool split the background paper into two, then "Tool too close..." should be
                # the error message and perhaps we shouldn't signal Shadow?
                if ERROR_MESSAGE.find("Shadow") != -1:                        
                    ERROR_MESSAGE = ERROR_MESSAGE.replace("Shadow? ", "")                
                    if ERROR_MESSAGE == "":
                        ERROR_THREAT = ""            
            # draw the contours and hulls in an image to make sure things are going okay
            # note that this image might be distorted since we have done perspective transform 
            # of the backgournd paper to the width/height ratio of the original image. But this
            # is OK since in Javascript we do the correct scaling.
            if ERROR_THREAT == "red":
                cv2.drawContours(transformed_img_with_cntrs_hulls, [c], -1, (0,0,255), 4)             
            else:
                cv2.drawContours(transformed_img_with_cntrs_hulls, [c], -1, (0,255,0), 4) 
            #cv2.drawContours(transformed_img_with_cntrs_hulls, [hull], -1, (255,0,255), 2)
            if MYENVIRONMENT == 'PYTHON':
                cv2.drawContours(transformed_img_with_cntrs_hulls, [hull], -1, (255,0,255), 2)
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':                
                plt.figure("final tool outlines and convexhulls");plt.imshow(transformed_img_with_cntrs_hulls, cmap='gray')
            
            points = np.squeeze(c)
            points = points.astype(float)
            x_min = np.min(points[:,0])
            y_min = np.min(points[:,1])
            hull_points = np.squeeze(hull)
            hull_points = hull_points.astype(float)
            
            # scale to the A4 paper size and flatten them so that we output a 1-dim array
            cntr_pts = []
            hull_pts = []
            for i in range(0,len(points[:,0])):
                # if flag is set to true scale to A4 size (for main ToolKaiser)
                if flag_scale == True:
                    points[i,0] = round(float(points[i,0] - x_min)*float(A4_w/sh_img[1]),2)
                    points[i,1] = round(float(points[i,1] - y_min)*float(A4_h/sh_img[0]),2)
                # else scale things by a factor of 10 to keep tools reasonable; used for processing images from web crawling
                else:
                    points[i,0] = round(float((points[i,0] - x_min)/10),2)
                    points[i,1] = round(float((points[i,1] - y_min)/10),2)
                # convert numpy values to naive Python types; this is needed while transmitting back to the client
                cntr_pts.append(points[i,0].item())
                cntr_pts.append(points[i,1].item())
            for i in range(0,len(hull_points[:,0])):  
                # if flag is set to true scale to A4 size (for main ToolKaiser)
                if flag_scale == True:
                    hull_points[i,0] = round(float(hull_points[i,0] - x_min)*float(A4_w/sh_img[1]),2)        
                    hull_points[i,1] = round(float(hull_points[i,1] - y_min)*float(A4_h/sh_img[0]),2)
                # else scale things by a factor of 10 to keep tools reasonable; used for processing images from web crawling
                else:
                    hull_points[i,0] = round(float((hull_points[i,0] - x_min)/10),2)        
                    hull_points[i,1] = round(float((hull_points[i,1] - y_min)/10),2)
                # convert numpy values to naive Python types; this is needed while transmitting back to the client
                hull_pts.append(hull_points[i,0].item())
                hull_pts.append(hull_points[i,1].item())
                
            # append the first point at the end to close the loop
            cntr_pts.append(points[0,0].item())
            cntr_pts.append(points[0,1].item())
            hull_pts.append(hull_points[0,0].item())
            hull_pts.append(hull_points[0,1].item())
            
            # create a list of the cntrs & hulls etc. if there are multiple objects in the image
            myrawdata.append(cntr_pts)
            myhulls.append(hull_pts)
            myoffsetX.append(x_min.item())
            myoffsetY.append(y_min.item())

        
        if MYENVIRONMENT == 'PYTHON':
            cv2.imwrite(os.path.join(folder_jpg_results,fname),transformed_img_with_cntrs_hulls)
        
        #print('length of various things:= ', len(mycntrs), len(myhulls), len(myoffsetX), len(myoffsetY))
        
        # ERROR_DETECION: not really error but just signal to the user that there are N number of tools found
        # Check: if at the end the number of tools > 1
        if final_number_of_tools > 1:
                ERROR_MESSAGE += "%d tools? " % final_number_of_tools
                
        return myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs    

# get the outline along the tool/object
def get_contours(im, fov, zoom_factor):

    ######### set error message to empty first ##########
    global ERROR_MESSAGE
    global ERROR_THREAT
    ERROR_MESSAGE = "" # default is empty -- no error
    ERROR_THREAT = ""
    
    ##############################################################################################
    ###################  Get the image and convert to appropriate size ###########################
    ##############################################################################################
    
    a = im.shape[0]
    b = im.shape[1]
    
    if DEBUG == True:
        print("fov:=", fov)
    
    # if image in landscape convert to portrait
    if a <= b:    
        im = imutils.rotate_bound(im, 90)    
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("original image");plt.imshow(im, cmap='gray')
    
    # resize image to 640 rows for faster processing; low resolution also makes several CV algos perform better
    # note that the original image is scaled to 640 rows (and the cols are scaled to maintain the aspect ratio)    
    a_new = 640    
    img = cv2.resize(im,(int(a_new*im.shape[1]/im.shape[0]), a_new))    
    
    #img = cv2.resize(im,(a_new,int(a_new*b/a)))    
    sh_img = img.shape
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('original size:',im.shape[0],im.shape[1])
        print('size after resizing to 640 rows: ', sh_img[0], sh_img[1])
    
    # get the grayscale image
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
    ##############################################################################################
    ###################  Get the background paper first ##########################################
    ##############################################################################################
    
    bkgrnd_box = get_background_paper(gray,img,sh_img)
    
    ##### check if the background_box area is smaller than its minimum bounding box #####
    vector_a = bkgrnd_box[0][0] - bkgrnd_box[0][1]
    vector_b = bkgrnd_box[0][0] - bkgrnd_box[0][3]
    row1 = np.asarray([vector_a[0], vector_a[1]])
    row2 = np.asarray([vector_b[0], vector_b[1]])
    matrix = np.asarray([row1, row2])
    area_of_background_box1 = np.abs(np.linalg.det(matrix)/2.0)
    vector_a = bkgrnd_box[0][2] - bkgrnd_box[0][1]
    vector_b = bkgrnd_box[0][2] - bkgrnd_box[0][3]
    row1 = np.asarray([vector_a[0], vector_a[1]])
    row2 = np.asarray([vector_b[0], vector_b[1]])
    matrix = np.asarray([row1, row2])
    area_of_background_box2 = np.abs(np.linalg.det(matrix)/2.0)
    total_area_background_box = area_of_background_box1+area_of_background_box2
    # get area of the minimum bounding box
    rect_background_box = cv2.minAreaRect(np.asarray(bkgrnd_box[0]))
    box_background_box = cv2.boxPoints(rect_background_box)
    box_background_box = np.int0(box_background_box)
    area_minimum_bounding_box = get_area(box_background_box)
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('total_area_background_box', total_area_background_box)
        print('area_minimum_bounding_box', area_minimum_bounding_box)
        
    # ERROR_DETECTION: phone tilted
    # check if area of the quadrilateral of final background_box is smaller than the minimum bounding box
    if total_area_background_box/area_minimum_bounding_box < 0.9:
        ERROR_MESSAGE += "Phone tilted? (%.2f) " % (total_area_background_box/area_minimum_bounding_box)
        ERROR_THREAT = "red"
        
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('final background trapezoid')
        print(len(bkgrnd_box[0]), bkgrnd_box)
    
    if USE_HIGH_RES == True:
        # get the background box in the original image
        scale_factor = im.shape[0]/img.shape[0]
        bkgrnd_box_original_image = []
        bkgrnd_box_original_image.append(scale_factor*bkgrnd_box[0])
        # get the area of the background paper for height estimation in mm
        area_of_background_box_in_px = np.linalg.norm(bkgrnd_box_original_image[0][0] - bkgrnd_box_original_image[0][1])*np.linalg.norm(bkgrnd_box_original_image[0][1] - bkgrnd_box_original_image[0][2])
        # assume that the paper is A4 (297x210)
        one_px_in_mm = np.sqrt(297*210/area_of_background_box_in_px)
        # actual length of the image in mm
        length_of_image_in_mm = one_px_in_mm*np.maximum(im.shape[0],im.shape[1])
        # estimate the height of the camera from the floor; this is used to warn the user if we are too close
        # ERROR_DETECTION: Phone too close? (d cm)
        # estimate the distance of camera to the floor and if it smaller than 60 cm, then signal error
        estimated_distance_in_mm = length_of_image_in_mm*zoom_factor/(2.0*np.tan(fov*math.pi/360))
        if estimated_distance_in_mm < 600:
            ERROR_MESSAGE += "Phone too close? (%.0f cm) " % np.round(estimated_distance_in_mm/10)
            ERROR_THREAT  = "red"
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('final background trapezoid in original image')            
            print(len(bkgrnd_box_original_image[0]), bkgrnd_box_original_image)
            print('one_px_in_mm:= ', one_px_in_mm)
            print('length_of_image_in_mm:= ', length_of_image_in_mm)
            print('estimated_distance_in_mm', ERROR_MESSAGE)
        
    
    # apply the perspective transform to remove the shear/rotation etc.
    # note: we have scaled the img to 640 rows (and cols are scaled according to the aspect ratio of original image)
    # hence when we do the perspective transform and get the image filled with the backgroud paper, we are 
    # distorting the image slightly. The image filled with background paper should ideally have the aspect ratio
    # of the paper, but it has the aspect ration of the original image. This is not an issue since we scale the 
    # image appropriately in Javascript. We maintain the current scaling so that we don't lose on the image processing
    if USE_HIGH_RES == False:
        img_new, gray_new = get_perspective_transform(bkgrnd_box, sh_img, img, gray)
    else:
        # get the grayscale image
        gray_high_res = cv2.cvtColor(im, cv2.COLOR_BGR2GRAY)
        img_new, gray_new = get_perspective_transform(bkgrnd_box_original_image, sh_img, im, gray_high_res)
        
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("image after perspective transform");plt.imshow(img_new, cmap='gray')
    
    ##############################################################################################
    ################### Get Tool Outline #########################################################
    ##############################################################################################
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('############### Tool outline detection ####################')
    # a little Gaussian smoothing to remove spurious edges
    blur = cv2.GaussianBlur(gray_new,(3,3),0) 
    # compute the median of the single channel pixel intensities
    v = np.median(blur)
    # apply automatic Canny edge detection using the computed median
    # use larger SIGMA to get more edges
    lower = int(max(0, (1.0 - 0.75) * v))
    upper = int(min(255, (1.0 + 0.75) * v))
    #lower = 100
    #upper = 200
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('lower %d, upper %d for tool edge detection on transformed image' % (lower, upper))
    edges = cv2.Canny(gray_new,lower,upper)    
    # "closing" transformation        
    # kernel for "closing" transformation 
    kernel_close = np.ones((7,7),np.uint8)
    img_for_tool_outline_edge = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)       
    
    transformed_img = np.copy(img_new)
    transformed_img_with_cntrs_hulls = np.copy(img_new)
        
    # clean up the boundary a little bit
    img_for_tool_outline_edge = clean_up_boundary(img_for_tool_outline_edge)    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("image for tool 1 detection, perspective -> edge detection");plt.imshow(img_for_tool_outline_edge, cmap='gray')   
    
    # use the downsampled (640 rows reshaped) image to find the edges for tool detection
    # this might be better than the image obtained from perspective transformation 
    # because we lose resolution. Do the perspective transform on the edge image thus obtained
    # so the previous is perspective transform and then edge detection
    # below is edge detection and then perspective transform
    if USE_DOWNSAMPLED_IMAGE == True:        
        # a little Gaussian smoothing to remove spurious edges        
        blur = cv2.GaussianBlur(gray,(3,3),0)             
        # compute the median of the single channel pixel intensities
        v = np.median(blur)
        # apply automatic Canny edge detection using the computed median
        # use larger SIGMA to get more edges
        lower = int(max(0, (1.0 - 2*SIGMA) * v))
        upper = int(min(255, (1.0 + 2*SIGMA) * v))
        #lower = 100
        #upper = 200
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('lower %d, upper %d for tool edge detection on original image' % (lower, upper))
        
        edges = cv2.Canny(gray,lower,upper) 
        # apply the perspective transform to remove the shear/rotation etc.
        img_new_downsampled, edges_new_downsampled = get_perspective_transform(bkgrnd_box, sh_img, img, edges)       
        
        # edges_new might not be completely binary due to perspective transformation
        # we can do thresholding to make it completely binary, but not needed perhaps
        #edges_new[edges_new > 0] = 255
        
        # "closing" transformation        
        # kernel for "closing" transformation 
        # originially used was (7,7)
        kernel_close = np.ones((9,9),np.uint8)
        img_for_tool_outline_edge_downsampled = cv2.morphologyEx(edges_new_downsampled, cv2.MORPH_CLOSE, kernel_close)
        # clean up the boundary a little bit
        img_for_tool_outline_edge_downsampled = clean_up_boundary(img_for_tool_outline_edge_downsampled)
        
        # lets do some sanity checking -- whether we want to use this downsampled image or not
        pruned_cntrs = []
        temp_pruned_cntrs = get_pruned_cntrs(img_for_tool_outline_edge,img_new)
        if len(temp_pruned_cntrs) > 0:
            pruned_cntrs.append(temp_pruned_cntrs)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('pruned cntrs on transformed img: ', len(pruned_cntrs))
            if len(pruned_cntrs)>0:
                for c in pruned_cntrs:
                    print(cntrperi(c), cntrarea(c))   
        pruned_cntrs_downsampled = [] 
        temp_pruneds_cntrs_downsampled = get_pruned_cntrs(img_for_tool_outline_edge_downsampled,img_new_downsampled)
        if len(temp_pruneds_cntrs_downsampled) > 0:
            pruned_cntrs_downsampled.append(temp_pruneds_cntrs_downsampled)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('pruned cntrs on original img: ', len(pruned_cntrs_downsampled))
            if len(pruned_cntrs_downsampled)>0:
                for c in pruned_cntrs_downsampled:                    
                    print(cntrperi(c), cntrarea(c))
        
        # if there was no contour found in the previous process, use the downsampled image
        if len(pruned_cntrs) == 0:
            print('using downsampled image before perspective transform for tool edge detection...')            
            img_for_tool_outline_edge = img_for_tool_outline_edge_downsampled            
            pruned_cntrs = pruned_cntrs_downsampled                   
        elif len(pruned_cntrs_downsampled) > 0:            
            # if the length (perimeter) of the largest contour in the downsampled image is more than twice
            # that of the same in the previous edge image, don't use the downsampled image
            if cntrperi(pruned_cntrs_downsampled[0])/cntrperi(pruned_cntrs[0]) < 1.0:
                print('using downsampled image before perspective transform for tool edge detection...')            
                img_for_tool_outline_edge = img_for_tool_outline_edge_downsampled
                pruned_cntrs = pruned_cntrs_downsampled
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print('using downsampled image before perspective transform for tool edge detection...')            
                    plt.figure("image for tool 1 detection, edge detection -> perspective");plt.imshow(img_for_tool_outline_edge, cmap='gray')
        
    # use the information in the segmented image tocombine with edge image for tool outline
    # this is useful when shadows are cast and the edge image doesn't give information about the
    # tool outline. segmentation will include the shadow which we can perhaps easily edit in the tool editor
    if USE_SEGMENTATION_TOOL_OUTLINE == True:
        # work on the image before the perspective transformation
        ret,th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        #img_seg = cv2.bitwise_not(th)        
        # "opening" transformation
        # kernel for "opening" transformation 
        kernel_open = np.ones((7,7),np.uint8)
        img_for_tool_outline_seg = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open)
        img_for_tool_outline_seg = cv2.bitwise_not(img_for_tool_outline_seg)
        # now do the perspective transformation
        img_new, img_for_tool_outline_seg_new = get_perspective_transform(bkgrnd_box, sh_img, img, img_for_tool_outline_seg)
        # clean up the boundary a little bit
        img_for_tool_outline_seg = clean_up_boundary(img_for_tool_outline_seg_new) 
                
        # add/take the OR of the two images -- edge image and segmented image
        img_for_tool_outline = cv2.bitwise_or(img_for_tool_outline_edge, img_for_tool_outline_seg)
        
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            plt.figure();plt.imshow(img_for_tool_outline_seg, cmap='gray')
            plt.figure();plt.imshow(img_for_tool_outline, cmap='gray')
    else:
        img_for_tool_outline = img_for_tool_outline_edge
        
    # get the contours around the tools/objects    
    if USE_DOWNSAMPLED_IMAGE == True:
        if len(pruned_cntrs) > 0:
            temp_cntr = pruned_cntrs[0].copy()
        else:
            temp_cntr = []
        pruned_cntrs = []
    else:        
        temp_cntr = get_pruned_cntrs(img_for_tool_outline,img_new)   
        pruned_cntrs = []
        
    num_of_tool = 2
    while (len(temp_cntr) > 0):        
        #print('temp_cntr size:= ', len(np.squeeze(temp_cntr)), len(temp_cntr))
        pruned_cntrs.append(temp_cntr)        
        # now take the convex hull of this contour
        hull = cv2.convexHull(temp_cntr)
        # have to decide whether we want to fill the convex hull with black or the contour itself with black
        ## fill in this convex hull black to effectively remove this contour from the img_for_tool_outline
        #cv2.drawContours(img_for_tool_outline, [hull], -1, (0,0,0), cv2.FILLED)
        # fill in this contour black to effectively remove this contour from the img_for_tool_outline
        cv2.drawContours(img_for_tool_outline, [temp_cntr], -1, (0,0,0), cv2.FILLED)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            figtitle = "image for tool " + str(num_of_tool) + " detection"
            plt.figure(figtitle);plt.imshow(img_for_tool_outline, cmap='gray')
        temp_cntr = []
        temp_cntr = get_pruned_cntrs(img_for_tool_outline,img_new)
        num_of_tool += 1
            
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pruned_cntrs:= ', len(np.squeeze(pruned_cntrs)))        

    ##############################################################################################
    ################### Prepare things to be sent to the tool editor in javascript ###############
    ##############################################################################################    
    
    if MYENVIRONMENT == 'PYTHON':     
        myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs = data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, True)             
        return transformed_img, myrawdata, myhulls, myoffsetX, myoffsetY, bkgrnd_box, sh_img, myprunedCntrs    
    else:            
        myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs = data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, True)        
        # we assume that the paper size is A4. The contours/outlines will be scaled to 297x210 (A4 size)
        # the image sent back to Javascript is not scaled to 297x210, but is scaled within the Javascript
        paper_size = "a4"        
        # we also send the dimensions of the original image in mm
        print("im (width,height)", im.shape[1], im.shape[0])
        width_in_mm = 210
        height_in_mm = 297
        # return both the image (background paper with tools) and image with outlines & convexhulls
        return transformed_img, myrawdata, myhulls, myoffsetX, myoffsetY, paper_size, width_in_mm, height_in_mm, transformed_img_with_cntrs_hulls, ERROR_MESSAGE
    

# process image grabbed from the web as it is
def proc_img_from_web(im):
    
    ##############################################################################################
    ###################  Get the image and convert to appropriate size ###########################
    ##############################################################################################
        
    print("shape:= ", im.shape[0], im.shape[1])
    #a_new = im.shape[0]    
    #img = cv2.resize(im,(int(a_new*im.shape[1]/im.shape[0]), a_new))    
    img = im
    
    #img = cv2.resize(im,(a_new,int(a_new*b/a)))    
    sh_img = img.shape
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('original size:',im.shape[0],im.shape[1])
        print('size after resizing to 640 rows: ', sh_img[0], sh_img[1])
    
    # get the grayscale image
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
      
    ##############################################################################################
    ################### Get Tool Outline #########################################################
    ##############################################################################################
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('############### Tool outline detection ####################')
    # a little Gaussian smoothing to remove spurious edges
    blur = cv2.GaussianBlur(gray,(3,3),0) 
    # compute the median of the single channel pixel intensities
    v = np.median(blur)
    # apply automatic Canny edge detection using the computed median
    # use larger SIGMA to get more edges
    lower = int(max(0, (1.0 - 0.75) * v))
    upper = int(min(255, (1.0 + 0.75) * v))
    #lower = 100
    #upper = 200
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('lower %d, upper %d for tool edge detection on transformed image' % (lower, upper))
    edges = cv2.Canny(gray,lower,upper)    
    # "closing" transformation        
    # kernel for "closing" transformation 
    kernel_close = np.ones((7,7),np.uint8)
    img_for_tool_outline_edge = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)       
    
    transformed_img = np.copy(img)
    transformed_img_with_cntrs_hulls = np.copy(img)
        
    # clean up the boundary a little bit
    img_for_tool_outline = clean_up_boundary(img_for_tool_outline_edge)    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("image for tool 1 detection, perspective -> edge detection");plt.imshow(img_for_tool_outline_edge, cmap='gray')       
        
    # get the contours around the tools/objects    
    pruned_cntrs = []
    temp_cntr = get_pruned_cntrs(img_for_tool_outline,img)
    
    num_of_tool = 2
    while (len(temp_cntr) > 0):
        pruned_cntrs.append(temp_cntr)
        # now take the convex hull of this contour
        hull = cv2.convexHull(temp_cntr)
        # have to decide whether we want to fill the convex hull with black or the contour itself with black
        ## fill in this convex hull black to effectively remove this contour from the img_for_tool_outline
        #cv2.drawContours(img_for_tool_outline, [hull], -1, (0,0,0), cv2.FILLED)
        # fill in this contour black to effectively remove this contour from the img_for_tool_outline
        cv2.drawContours(img_for_tool_outline, [temp_cntr], -1, (0,0,0), cv2.FILLED)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            figtitle = "image for tool " + str(num_of_tool) + " detection"
            plt.figure(figtitle);plt.imshow(img_for_tool_outline, cmap='gray')
        temp_cntr = []
        temp_cntr = get_pruned_cntrs(img_for_tool_outline,img)
        num_of_tool += 1
            
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pruned_cntrs %d' % (len(np.squeeze(pruned_cntrs))))        
        
    ##############################################################################################
    ################### Prepare things to be sent to the tool editor in javascript ###############
    ##############################################################################################    
    
    if MYENVIRONMENT == 'PYTHON':     
        myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs = data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, False);
        return transformed_img, myrawdata, myhulls, myoffsetX, myoffsetY, bkgrnd_box, sh_img, myprunedCntrs    
    else:            
        myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs = data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, False);
        # we assume that the paper size is A4. The contours/outlines will be scaled to 297x210 (A4 size)
        # the image sent back to Javascript is not scaled to 297x210, but is scaled within the Javascript        
        print("im (width,height)", im.shape[1], im.shape[0])
        paper_size = "special"        
        # we also send the dimensions of the original image in mm
        width_in_mm = im.shape[1]/10
        height_in_mm = im.shape[0]/10
        return transformed_img, myrawdata, myhulls, myoffsetX, myoffsetY, paper_size, width_in_mm, height_in_mm, transformed_img_with_cntrs_hulls, ERROR_MESSAGE
        

if MYENVIRONMENT == 'PYTHON':
    print('Inside the __main__ function!')
    if __name__ == '__main__':        
        print('python version:= ', sys.version)
        # folders for all the input data and output results    
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//All Tools'
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools new jpeg'
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools jpeg'
        folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Library//ALL Home Depot JPG'
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools trouble jpeg' 
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_input'
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_Error_Codes'        
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_Error_Codes_Bad'
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_Light_Background'        
        #folder_jpg_results = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Debugging//Test_output'      
        folder_jpg_results = 'D://home//work//DigitalHome//DigitalToolBox//Test_output_1'      
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            fname = 'IMG_8100.jpg'
            print(fname) 
            im = cv2.imread(os.path.join(folder_jpg,fname))
            fov = 0
            zoom_factor = 1.0
            cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, bkgrnd_box, sh_img, myprunedCntrs = get_contours(im, fov, zoom_factor) 
            print("ERROR_MESSAGE:= ", ERROR_MESSAGE)
            print("ERROR_THREAT:= ", ERROR_THREAT)
        else:
            folder_ErrorMessage_results = 'D://home//work//DigitalHome//DigitalToolBox'            
            fileErrMsg = open(os.path.join(folder_ErrorMessage_results,"errorMessages_1.txt"),'w')
            for fname in os.listdir(folder_jpg):                                        
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"): 
                    print(fname) 
                    im = cv2.imread(os.path.join(folder_jpg,fname))
                    fov = 0
                    zoom_factor = 1.0
                    cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, bkgrnd_box, sh_img, myprunedCntrs = get_contours(im, fov, zoom_factor)                   
                    print("ERROR_MESSAGE:= ", ERROR_MESSAGE)
                    if ERROR_MESSAGE != "":                        
                        fileErrMsg.write('%s %s\n\n' % (fname,ERROR_MESSAGE))
                    ERROR_MESSAGE = ""
                    ERROR_THREAT = ""
                    #cv2.imwrite(os.path.join(folder_jpg_results,fname),cvImagewithCntrs)                
                    # write to fname.txt when debug mode is on
                    if TESTING == True and MYENVIRONMENT == 'PYTHON': 
                        #folder_txt_results = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools test txt'
                        folder_txt_results = 'D://home//work//DigitalHome//DigitalToolBox//Test_txt_output_1'
                        filename = os.path.splitext(fname)[0]
                        file = open(os.path.join(folder_txt_results,filename+".txt"),'w')
                        # write the size of the original image
                        file.write('%d %d # orig image (rows,cols) # \n' % (sh_img[0],sh_img[1]))
                        # write the background box coordinates              
                        if len(bkgrnd_box) > 0:
                            for i in range(0,len(bkgrnd_box[0])):
                                file.write('%d %d ' % (bkgrnd_box[0][i,0],bkgrnd_box[0][i,1]))
                            file.write('# background paper coordinates # \n')
                        else:
                            file.write('-1 # background paper coordinates # \n')
                        # write the number of tools 
                        file.write('%d # number of tools # \n' % (len(myprunedCntrs)))
                        # write the offsetx and offsety                        
                        for i in range(0,len(myprunedCntrs)):
                            #print("i:= ", i)
                            file.write('%.2f %.2f # offsetX and offsetY # \n' % (myoffsetX[i], myoffsetY[i]))
                        # write the convex hulls
                        i = 0
                        for c in myprunedCntrs:
                            # get the convex hull
                            hull = cv2.convexHull(c) 
                            hull = np.squeeze(hull)
                            file.write('%d ' % (len(hull)))
                            file.write('# number of points in convexhull no. %d # \n' % (i+1))
                            for j in range(0,len(hull)):
                                file.write('%.2f %.2f ' % (hull[j][0], hull[j][1]))
                            file.write('# convexhull no. %d # \n' % (i+1))
                            i = i+1
                        # write the tool path
                        i = 0
                        for c in myprunedCntrs:  
                            cntr = np.squeeze(c)
                            file.write('%d %.2f %.2f ' % (len(cntr), cntrperi(cntr), cntrarea(cntr)))
                            file.write('# number of points, perimeter, area of contour no. %d # \n' % (i+1))                                  
                            for j in range(0,len(cntr)):
                                file.write('%.2f %.2f ' % (cntr[j][0], cntr[j][1]))
                            file.write('# contour no. %d # \n' % (i+1))
                            i = i+1
                        file.close()            
                else:
                    print("Error: Need input files to be in jpeg format")
            fileErrMsg.close()

