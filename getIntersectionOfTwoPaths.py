# -*- coding: utf-8 -*-
"""
Created on Sun May 10 10:34:10 2020

@author: 
"""


from shapely.geometry import Polygon
import matplotlib.pyplot as plt
import os

folder_input = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Overlaps'
folder_output = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Overlaps'

#folder_input = 'D://home//work//DigitalHome//DigitalToolBox//Test_input'
#folder_output = 'D://home//work//DigitalHome//DigitalToolBox//Test_output'
fileout = open(os.path.join(folder_output,"overlapFromPython.txt"),'w')

# string to write output from python
overlap = ""
#for fname in os.listdir(folder_input):        
    #if fname.endswith(".txt") or fname.endswith(".TXT"): 
    # read the overlap data from JS and store it line by line in lines
fname = "overlapFromJavascript.txt"
lines = open(os.path.join(folder_input,fname)).read().splitlines()
# output/print the line number where we had a mismatch between JS and python
i = 1
# go over all the lines
for data in lines:
    if i%10000 == 1:             
        print(i)
    #if i == 492 or i == 1062 or i == 1632 or i == 2202:
    #    i += 1
    #    continue            
    # split data according to ;
    paths = data.split(';')            
    # 0th field is the overlap area calculated from JS
    # 1st field is the first path            
    # convert string to a list of floats
    path_floats = [float(x) for x in paths[1].split(',')]            
    # merge the x,y coordinates to get a list of tuples needed for intersection library
    x = path_floats[0::2]
    y = path_floats[1::2] 
    p = list(zip(x,y))

    # 2nd field is the second path
    path_floats = [float(x) for x in paths[2].split(',')]                                 
    # merge the x,y coordinates to get a list of tuples needed for intersection library
    x = path_floats[0::2]
    y = path_floats[1::2] 
    q = list(zip(x,y))
    
    # 3rd field is the intersection path from JS
    if len(paths[3]) > 0:
        pathJS = [float(x) for x in paths[3].split(',')] 
        # merge the x,y coordinates to get a list of tuples needed for intersection library
        x = pathJS[0::2]
        y = pathJS[1::2] 
        rFromJS = list(zip(x,y))
        rFromJS = Polygon(rFromJS)   
    
    # get the polygon object from each path
    p = Polygon(p)
    q = Polygon(q)        
    # calculate the intersection path using Python library
    r = p.intersection(q)
    
    # get the area of the intersection path (overlap area)
    overlap += str(round(r.area/10000,3))+";"
    overlap += paths[1]+";"
    overlap += paths[2]+"\n"
    
    # if mismatch between JS and Python plot it and save it
    #print("Overlaps (Python,JS) (%.10f,%.10f,%.10f)" % (round(r.area/10000,3), float(paths[0]), abs((round(r.area/10000,3)-float(paths[0])))))
    if abs(round(r.area/10000,3) - float(paths[0])) > 0.01:
        print("Warning, overlaps are not matching! (%f,%f,%d)" % (round(r.area/10000,3), float(paths[0]), i))
        # plot first path in red
        x,y = p.exterior.xy
        plt.plot(x,y,'r')
        # plot second path in green
        x,y = q.exterior.xy
        plt.plot(x,y,'g')          
        # plot intersection from JS in blue but fill it so that we can distinguish it from Python
        if len(paths[3]) > 0:
            x,y = rFromJS.exterior.xy
            plt.fill(x,y,'b',linewidth=3)
        # check if the Python object is a list of polygons
        if r.geom_type == 'MultiPolygon':
            for geom in r.geoms:    
                x,y = geom.exterior.xy
                plt.plot(x,y,'k',linewidth=3)
        else:
            x,y = r.exterior.xy
            plt.plot(x,y,'k',linewidth=3)
        # save the plot
        fileErr = 'overlapDiff_' + str(i) + '.png'
        plt.savefig(os.path.join(folder_output,fileErr))
        plt.close()
    elif round(r.area/10000,3) > 1.0:
        print("Warning, python overlap larger than 1.0! (%f,%f,%d)" % (round(r.area/10000,3), float(paths[0]), i))
    elif float(paths[0]) > 1.0:
        print("Warning, javascript overlap larger than 1.0! (%f,%f,%d)" % (round(r.area/10000,3), float(paths[0]), i))
        
    i += 1
                        
fileout.write(overlap)
fileout.close()