# -*- coding: utf-8 -*-
"""
Created on Mon Mar  2 13:23:04 2020

@author: SK & RU
"""

import numpy as np
import cv2
import imutils
import os
import matplotlib.pyplot as plt
import itertools
import sys
import math

MYENVIRONMENT = 'HEROKU' # for pushing onto herokukaiser and deploying on heroku
MYENVIRONMENT = 'PYTHON' # delete for heroku # set LOCAL to run things on locolhost; set PYTH<PERSON> to run things with dropbox data; set CRAWL to process imgs grabbed from web
TESTING = False # testing for .txt output with image size; background paper size; offsetx; offsety; convex hull data; raw path data
DEBUG = True # for detail debugging of one example with pictures
SIGMA = 0.33 # for Canny edges detection
THRESH_CONTOUR = 50 # for removing really small contours
USE_SEGMENTATION = True # segmentation or edge detection
USE_SEGMENTATION_TOOL_OUTLINE = False # use segmentation also for tool outline detection
USE_DOWNSAMPLED_IMAGE = True # use the downsampled image before perspectvive transformation for tool edge detection
THRESH_CONTOURAREA_BOXAREA = 0.01 # for removing contours which have contour area much smaller than the bounding box
USE_HIGH_RES = True # use the original high resolution image (3024x4024)
RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR = 1 # used to keep track of ratio of max_cntr and second_max_cntr found in background paper routine
USE_HIST_EQUALIZATION = True # use contrast enhancement for shiny tools

# size of A4 paper in mm
A4_h = 297
A4_w = 210

# how many px make one mm on the screen
pxpermm = 96/25.4

# error message to be sent back -- background paper clipped; floor contrast etc. 
ERROR_MESSAGE = "" # default is empty -- no error
ERROR_THREAT = "" # default is empty -- no threat; low = color orange; high = color red

# perimeter length of the contour
def cntrperi(cnt):
   return cv2.arcLength(cnt,True)

# area of the contour
def cntrarea(cnt):
    return cv2.contourArea(cnt)

# get the area of a rectangular box
def get_area(box):
    h = np.linalg.norm(box[1]-box[2])
    w = np.linalg.norm(box[2]-box[3])
    
    return h*w

# Finds the intersection of two lines given in Hesse normal form. Returns closest integer pixel locations.
# See https://stackoverflow.com/a/383527/5087436
def intersection(line1, line2):

    rho1 = line1[0]; theta1 = line1[1]
    rho2 = line2[0]; theta2 = line2[1]
    A = np.array([
        [np.cos(theta1), np.sin(theta1)],
        [np.cos(theta2), np.sin(theta2)]
    ])        
    b = np.array([[rho1], [rho2]])
    #print(A,b,np.linalg.det(A))
    x0, y0 = np.linalg.solve(A, b)
    x0, y0 = int(np.round(x0)), int(np.round(y0))
    return np.array([x0, y0],dtype=np.int32)


def plot_lines(image, pruned_lines):
    #Display on the original image
    sh = image.shape
    for i in range(0,len(pruned_lines)):
       rho = pruned_lines[i][0]
       theta = pruned_lines[i][1]
       a = np.cos(theta)
       b = np.sin(theta)
         
       x0 = a*rho
       y0 = b*rho
       x1 = int(x0 + np.maximum(sh[0],sh[1])*(-b))
       y1 = int(y0 + np.maximum(sh[0],sh[1])*(a))
       x2 = int(x0 - np.maximum(sh[0],sh[1])*(-b))
       y2 = int(y0 - np.maximum(sh[0],sh[1])*(a))
       # overlay line on original image
       cv2.line(image,(x1,y1),(x2,y2),(0,255,0),2)

# check if line l is "contained" in the set of lines
def is_contained_in_lines_seg(myl, mylines, sh_img, TOLERANCE):
    
    if len(mylines) == 0:
        return False    
    
    # first make sure things are ok when rho is negative
    if myl[0] < 0:        
        myl[1] = myl[1] - np.pi             
        myl[0] = np.abs(myl[0])
            
    for i in range(0,len(mylines)):
        if mylines[i][0] < 0:
            mylines[i][0] = np.abs(mylines[i][0])
            mylines[i][1] = mylines[i][1] - np.pi
            
    temp_lines = np.abs(mylines - myl)    
    if (np.min(np.abs(temp_lines[:,0]/np.maximum(sh_img[0],sh_img[1])) + np.abs(temp_lines[:,1]/np.pi))) <= TOLERANCE:
        return True
    else:
        return False


# perspective transform: we have four corners of the 640x480 rectangle as the 
# destination and the bkgrnd_box as the source (four) corner points
# use getPerspectiveTransform to get the projection from src -> dst.
# In this projective transform, straight lines remain as straight lines,
# consequently we also get the cropped image lying within the bkgrnd_box duly transformed
def get_perspective_transform(bkgrnd_box, sh_dst, img, gray):                  

    global ERROR_MESSAGE
    global ERROR_THREAT
    
    # 640 rows x 480 cols are arranged in anti-clockwise order
    pts2 = np.float32([[0,0], [0,sh_dst[0]], [sh_dst[1],sh_dst[0]], [sh_dst[1],0]])
    pts1 = -1*np.ones(np.shape(pts2))
   
    # find the long edge of the bkgrnd_box
    if (np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][1]) >= np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][2])):
        # edge 0-1 is larger than edge 1-2        
        pts1[0,:] = bkgrnd_box[0][0]
        pts1[1,:] = bkgrnd_box[0][1]
        pts1[2,:] = bkgrnd_box[0][2]
        pts1[3,:] = bkgrnd_box[0][3]
        pts1 = np.float32(pts1) 
        M = cv2.getPerspectiveTransform(pts1,pts2)
        if (np.linalg.det(M) < 0):
            # change orientation
            pts1[0,:] = bkgrnd_box[0][1]
            pts1[1,:] = bkgrnd_box[0][0]
            pts1[2,:] = bkgrnd_box[0][3]
            pts1[3,:] = bkgrnd_box[0][2]
            pts1 = np.float32(pts1) 
    else:        
        # edge 0-1 is smaller than edge 1-2
        pts1[0,:] = bkgrnd_box[0][1]
        pts1[1,:] = bkgrnd_box[0][2]
        pts1[2,:] = bkgrnd_box[0][3]
        pts1[3,:] = bkgrnd_box[0][0]
        pts1 = np.float32(pts1) 
        M = cv2.getPerspectiveTransform(pts1,pts2)
        if (np.linalg.det(M) < 0):
            # change orientation
            pts1[0,:] = bkgrnd_box[0][2]
            pts1[1,:] = bkgrnd_box[0][1]
            pts1[2,:] = bkgrnd_box[0][0]
            pts1[3,:] = bkgrnd_box[0][3]     
            pts1 = np.float32(pts1)             
      
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pts1:= ', pts1)
        print('pts2:= ', pts2)
            
    M = cv2.getPerspectiveTransform(pts1,pts2)
    if (np.linalg.det(M) < 0):
        print('Perspective transform: there are still flips!!!')
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('perspective transform matrix:= ', M, np.linalg.det(M))
            
    gray_new = cv2.warpPerspective(gray,M,(sh_dst[1],sh_dst[0]))
    img_new = cv2.warpPerspective(img,M,(sh_dst[1],sh_dst[0]))
    
    return img_new, gray_new

# perspective transform: if we find that perspective transform has flipped things, unflip by 
# doing either vertical OR horizontal flip. This should make product of flips = 1
def get_perspective_transform_with_flip(bkgrnd_box, sh_dst, img, gray):                  

    # 640 rows x 480 cols are arranged in anti-clockwise order
    pts2 = np.float32([[0,0], [0,sh_dst[0]], [sh_dst[1],sh_dst[0]], [sh_dst[1],0]])
    pts1 = -1*np.ones(np.shape(pts2))
   
    # find the long edge of the bkgrnd_box
    if (np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][1]) >= np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][2])):
        # edge 0-1 is larger than edge 1-2        
        pts1[0,:] = bkgrnd_box[0][0]
        pts1[1,:] = bkgrnd_box[0][1]
        pts1[2,:] = bkgrnd_box[0][2]
        pts1[3,:] = bkgrnd_box[0][3]        
    else:        
        # edge 0-1 is smaller than edge 1-2
        pts1[0,:] = bkgrnd_box[0][1]
        pts1[1,:] = bkgrnd_box[0][2]
        pts1[2,:] = bkgrnd_box[0][3]
        pts1[3,:] = bkgrnd_box[0][0]        
      
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pts1:= ', pts1)
        print('pts2:= ', pts2)

    pts1 = np.float32(pts1)             
    M = cv2.getPerspectiveTransform(pts1,pts2)
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('perspective transform matrix:= ', M, np.linalg.det(M))

    gray_new = cv2.warpPerspective(gray,M,(sh_dst[1],sh_dst[0]))
    img_new = cv2.warpPerspective(img,M,(sh_dst[1],sh_dst[0]))
    
    if (np.linalg.det(M) < 0):
        print('Perspective transform: there is a flip...unflipping!!!')
        # any flip would unflip things; so do a flip along y-axis
        img_new = cv2.flip(img_new, 0)
        gray_new = cv2.flip(gray_new, 0)
    
    return img_new, gray_new


# for images where object has a little part touching the background paper 
# we cleanup the boundary by setting a small sliver at each side to 0
def clean_up_boundary(im):
    # just make a few pixels around the boundary black
    THRESH_BNDRY = 2 
    im[0:THRESH_BNDRY, :] = 0
    im[im.shape[0]-THRESH_BNDRY:im.shape[0], :] = 0
    im[:,0:THRESH_BNDRY] = 0
    im[:,im.shape[1]-THRESH_BNDRY:im.shape[1]] = 0
    
    return im

# use the convexity defect function to improve the convex hull
def get_better_hull(cntr):
    # get the hull coordinates
    temp_hull = cv2.convexHull(cntr,returnPoints = False) 
    # get the convexity defect points
    defects = cv2.convexityDefects(cntr,temp_hull)
    # get the actual hull coordinates as well
    hull = cv2.convexHull(cntr)  
             
    # if we found defects then proceed to add them
    if defects is not None:
        for i in range(defects.shape[0]):
            s,e,f,d = defects[i,0]
            lstart = cntr[s][0]
            lend = cntr[e][0]
            far = cntr[f][0]
            dist = np.linalg.norm(np.cross(lend - lstart, far - lstart)/np.linalg.norm(lend - lstart))
            #if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            #    print('all defects ', i, lstart, lend, far, s, e)
            if dist >= (640/297)*10: # include the defect point in the hull if it is more than 1cm away                    
                #ind = np.where(((hull[:,0,0]==lend[0]) & (hull[:,0,1]==lend[1])))
                ind_end = np.where(((np.abs(hull[:,0,0]-lend[0]) < 1e-4) & (np.abs(hull[:,0,1]-lend[1]) < 1e-4)))              
                ind_start = np.where(((np.abs(hull[:,0,0]-lstart[0]) < 1e-4) & (np.abs(hull[:,0,1]-lstart[1]) < 1e-4))) 
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print('ind_start ind_end ', ind_start, ind_end)
                
                # the first case should ideally never happen, but it does, why??            
                if len(ind_start[0]) > 1:
                    # something went wrong, just ignore this
                    continue
                else:                        
                    ind_start = int(ind_start[0]) 
                if len(ind_end[0]) > 1:
                    # something went wrong, just ignore this
                    continue
                else:                        
                    ind_end = int(ind_end[0]) 
                    
                # move the start and the end points a little inside, so that we can get a 'peek' at the defect
                a = lstart + 0.07*(lend - lstart)            
                a = a.astype(int)
                b = lend + 0.07*(lstart - lend)            
                b = b.astype(int)  
                
                # normal vector to lend - lstart; note that lend to lstart might be a large vector
                # hence we move the defect point only by a tiny fraction (1% for now)
                d = np.array([-1.0*(lend[1] - lstart[1]), 1.0*(lend[0] - lstart[0])])
                d = 0.01*d
                d[0] = np.round(d[0])
                d[1] = np.round(d[1])
                d = d.astype(int)
                far = far + d
                
                if ind_end < ind_start:
                    hull = np.concatenate((hull[0:ind_end+1,:,:], b.reshape((1,1,2)), far.reshape((1,1,2)), a.reshape((1,1,2)), hull[ind_end+1:len(hull),:,:]))
                else:
                    hull = np.concatenate((hull[0:ind_start+1,:,:], a.reshape((1,1,2)), far.reshape((1,1,2)), b.reshape((1,1,2)), hull[ind_start+1:len(hull),:,:]))
            
    return hull

# check closeness of boxes of max_box and box
def check_closeness(max_hull, hull, max_cntr, c, img_copy):
    
    # tolerance to determine closeness (this is 3mm)
    TOLERANCE_CLOSE = -1.0*np.ceil(640/297)
    flag = False     
    
    # pointPolygonTest gives the shortest signed distance between a point and a contour
    # if distance is negative it is outside, if positive then inside, if 0 then on the contour
    # check if any point in the hull is inside max_hull by at least TOLERANCE_CLOSE    
    for pt in hull:        
        dist = cv2.pointPolygonTest(np.asarray(max_hull),tuple(np.squeeze(pt)),True)
        if dist >= TOLERANCE_CLOSE:
            flag = True
    # check if any point in the max_hull is inside hull by at least TOLERANCE_CLOSE
    for pt in max_hull:
        dist = cv2.pointPolygonTest(np.asarray(hull),tuple(np.squeeze(pt)),True)
        if dist >= TOLERANCE_CLOSE:
            flag = True   
    
    # if flag is true, then further scrutinize by considering improved convex hull (with defects added)
    # think of the case where a screwdriver is "inside" a plier
    if flag == True:
        flag = False        
        max_hull_better = get_better_hull(max_cntr)
        hull_better = get_better_hull(c)
        # now run the above closeness routine one more time
        # check if any point in the hull_better is inside max_hull_better by at least TOLERANCE_CLOSE            
        for pt in hull_better:        
            dist = cv2.pointPolygonTest(np.asarray(max_hull_better),tuple(np.squeeze(pt)),True)            
            if dist >= TOLERANCE_CLOSE:
                flag = True
        # check if any point in the max_hull_better is inside hull_better by at least TOLERANCE_CLOSE
        for pt in max_hull_better:
            dist = cv2.pointPolygonTest(np.asarray(hull_better),tuple(np.squeeze(pt)),True)            
            if dist >= TOLERANCE_CLOSE:
                flag = True  
                
        # draw the better hulls for debuggin purpose
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':                         
            cv2.drawContours(img_copy, [hull_better], 0, (0,0,0), 4)
            cv2.drawContours(img_copy, [max_hull_better], 0, (0,0,0), 4)
            plt.figure();plt.imshow(img_copy, cmap='gray')
        
    return flag


# check if any other contour can be merged with the max_cntr
def merge_cntrs(max_cntr, other_cntrs, img_copy):
    
    # find box/rect around the max_cntr call it max_box    
    max_hull = cv2.convexHull(max_cntr)      
      
    # go over the other_cntrs and check if any rect/box around that cntr is close to the max_cntr
    # if "close" then merge it with the max_cntr
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('other_cntrs',len(other_cntrs))
    for c in other_cntrs:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('candidate cntr for merging: ', len(c))        
        hull = cv2.convexHull(c)             
        # check if one corner of the box is close to any one of the corners of the max_box          
        #if (check_closeness(max_box, box, sh) and (get_area(box) <= 0.4*get_area(max_box))):
        # check if any point in one hull is close to the other hull and also make sure the 
        # area of the candidate contour to be merged is less than 80% of the max contour
        if (check_closeness(max_hull, hull, max_cntr, c, img_copy) and (cntrarea(c) <= 0.8*cntrarea(max_cntr)) ):
        #if (check_closeness(max_box, box, sh) and (cntrarea(c) <= 0.5*cntrarea(max_cntr))):
            # boxes are close and box is "smaller" compared to max_box and we now merge them
            max_cntr = np.concatenate((max_cntr,c),axis=0)   
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                print('cntr was merged with max_cntr')
    return max_cntr


# return the max contour possibly merged with neighboring contours
def get_pruned_cntrs(image,image_orig):
        
    img_copy = np.copy(image_orig)    
    # get contours around the objects
    major = cv2.__version__.split('.')[0]
    if major == '3':
        im2, cnts, hier = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        cnts, hier = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrperi, reverse = True)[:20] #sort the top 20
    
    max_cntr = []
    other_cntrs = [] # list of all cntrs which are not the max_cntr
    max_area = 0
    for c in cnts:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON': 
            rect = cv2.minAreaRect(c)            
            box = cv2.boxPoints(rect)
            box = np.int0(box) 
            cv2.drawContours(img_copy, [c], 0, (0,255,0), 4) 
            #print(cntrperi(c), cntrarea(c), box)
            cv2.drawContours(img_copy, [box], 0, (0,0,255), 4)
    	# if the contour has really small perimeter, discard it
        if cntrperi(c) > THRESH_CONTOUR:                       
            rect = cv2.minAreaRect(c)            
            box = cv2.boxPoints(rect)
            box = np.int0(box)                  
            area = cntrarea(c)
            #if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            #    print('area', area, 'first_pt', c[0], 'last_pt', c[len(c)-1], 'box', box)
            if (area >= max_area):                
                # found a contour of larger size
                # put the previous max_cntr, if not empty, into other_cntrs
                if (len(max_cntr)>0):
                    other_cntrs.append(max_cntr)
                max_area = area
                max_cntr = c
            else:
                if (len(max_cntr)>0):
                    other_cntrs.append(c)
                
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':        
        print('number of other cntrs: ',len(other_cntrs))
        for c in other_cntrs:
            print('first point in other cntr: ', c[0])
        print('max_cntr length: ',len(np.squeeze(max_cntr))) 
        if len(max_cntr) > 0:
            print('first point in max_cntr: ', max_cntr[0])
        plt.figure();plt.imshow(img_copy, cmap='gray')
    
    # check if any from the other_cntrs can be merged with the max_cntr
    if (len(other_cntrs) > 0):
        max_cntr = merge_cntrs(max_cntr, other_cntrs, img_copy)
    
    return max_cntr


# check if intersection of two lines lies outside the image
def intersectWithTwoLines(myline, mylistoflines, sh):    
    count_intersection = 0
    for line in mylistoflines:    
        # ignore lines which have theta very close to each other
        if (np.abs(myline[1]-line[1])) > 1e-4: 
            # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
            if (np.abs(1/np.tan(myline[1])-1/np.tan(line[1]))) > 1e-4:                 
                a = intersection(myline, line)                
                # count the number of intersections inside the image
                if (a[0] < sh[1] and a[0] > 0 and a[1] > 0 and a[1] < sh[0]):            
                    count_intersection += 1        
        
    return count_intersection
            

# get the largest contour in terms of area enclosed
def get_largest_cntr(pruned_cntrs):
        
    max_area = cntrarea(pruned_cntrs[0])
    if len(pruned_cntrs) == 1:
        return pruned_cntrs
    else:
        for c in pruned_cntrs:
            if cntrarea(c) >= max_area:
                largest_cntr = c
                max_area = cntrarea(c)
                #print(max_area)
    return [largest_cntr]

# get Hough lines from the edge detection image
def get_lines_edge_image(gray, blur_size, hough_threshold, sh_img):
    # Gaussian smoothing to remove spurious edges
    blur = cv2.GaussianBlur(gray,(blur_size,blur_size),0) 
    # compute the median of the single channel pixel intensities
    v = np.median(blur)
    # apply automatic Canny edge detection using the computed median
    # use SIGMA = 0.33 here 
    lower = int(max(0, (1.0 - SIGMA) * v))
    upper = int(min(255, (1.0 + SIGMA) * v))
    #lower = 100
    #upper = 200
    edges = cv2.Canny(blur,lower,upper)    
    # "closing" transformation        
    # kernel for "closing" transformation 
    kernel_close = np.ones((7,7),np.uint8)
    img_for_bkgrnd_edge = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)
    
    image = img_for_bkgrnd_edge
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('blur_size %d hough_threshold %d lower %d, upper %d for bkgrnd edge detection' % (blur_size, hough_threshold, lower, upper))
        figtitle="edge image for bkgrnd box detection blur_size = " + str(blur_size) + " hough_thresh = " + str(hough_threshold)
        plt.figure(figtitle);plt.imshow(img_for_bkgrnd_edge, cmap='gray')
    
    # get the Hough lines on the edge image
    lines_edge = cv2.HoughLines(image,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/hough_threshold)) 
    if lines_edge is not None:
        lines_edge = np.squeeze(lines_edge)
    
    return lines_edge

# check if there are 4 distinct lines that we want or not (this is mostly when we have around <50 lines)
def get_num_distinct_lines(lines_edge):
    
    num = len(lines_edge) # starting number of distinct lines
    distinct_lines = np.ones(num,dtype=int) # all are distinct at the start
    #print(distinct_lines)
    for i in range(0,num):        
        if lines_edge[i][0] < 0:            
            thetai = np.pi - lines_edge[i][1]
        else:
            thetai = lines_edge[i][1]
        for j in range(i+1,num):
            if distinct_lines[j] == 1:
                flag_slope = False
                if lines_edge[j][0] < 0:            
                    thetaj = np.pi - lines_edge[j][1]
                else:
                    thetaj = lines_edge[j][1]
                # sometimes convention of only positive theta is not followed hence we have second condition below
                if np.abs(thetai-thetaj) < 1e-2 or np.abs(1/np.tan(thetai)-1/np.tan(thetaj)) < 1e-2: 
                    flag_slope = True  
                #print(i,j,np.abs(np.abs(lines_edge[i][0]) - np.abs(lines_edge[j][0])),flag_slope)                                  
                if (np.abs(np.abs(lines_edge[i][0]) - np.abs(lines_edge[j][0])) < 5 and flag_slope == True):
                    distinct_lines[j] = 0
    #print(distinct_lines, len(np.nonzero(distinct_lines)[0]))
    return len(np.nonzero(distinct_lines)[0])

# check if the lines intersect in a reasonable quadrilateral 
# i.e., each line intersects, within the image, with exactly two other lines
def check_quadrilateral(lines, sh_img):
    
    flag = True # default is that we have a reasonable quadrilateral
    
    for i in range(0,len(lines)):         
        num_intersecting_lines= 0
        for j in range(0,len(lines)):
            if j == i:
                continue
            line1 = lines[i]; line2 = lines[j]
            # make sure they aren't parallel lines
            if np.abs(line1[1] - line2[1]) > 1e-4: 
                # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
                if (np.abs(1/np.tan(line1[1])-1/np.tan(line2[1]))) > 1e-4: 
                    a = intersection(line1, line2)
                    #print('pts of intersection: ', a)
                    if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):
                        num_intersecting_lines += 1
        # if we have a line with 0 or 1 or 3 intersections then something is wrong
        if num_intersecting_lines != 2:
            flag = False
            break
        
    return flag

# get the background paper from the image
def get_background_paper(gray,img, sh_img):
    
    global ERROR_MESSAGE
    global ERROR_THREAT
    global RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR
    # two ways to get the background paper; currently using the segmentation/thresholding approach
    if USE_SEGMENTATION:
        # simple thresholding for segmentation                 
        ret,th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)    
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':            
            plt.figure("grayscale original image");plt.imshow(gray, cmap='gray')
            plt.figure("histogram of gray values");plt.hist(gray.ravel(),256)
            print('binary threshold = ', ret)
        #img_seg = cv2.bitwise_not(th)        
        # "opening" transformation
        # kernel for "opening" transformation 
        #if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        #    plt.figure();plt.imshow(th, cmap='gray')
        kernel_open = np.ones((7,7),np.uint8)
        img_for_bkgrnd = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            figtitle = "segmentation/thresholding, kernel = "+str(7)
            plt.figure(figtitle);plt.imshow(img_for_bkgrnd, cmap='gray')
        #opening = cv2.morphologyEx(closing, cv2.MORPH_OPEN, kernel_open)
    else:
        # failsafe approach for low contrast paper detection
        # convert to HSV colorspace     
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        # get the different channels
        h, s, v = cv2.split(hsv)
        # the paper should mostly have uniform hue, so we use this information
        # but first we need to do the following: for hsv values with low saturation (say < 50 or 20%), 
        # we will convert the hue values in the range [0,10] to [169,179]. This is because the hue is on 
        # a cylinder and wraps around. 
        hist_v = cv2.calcHist([v],[0],None,[256],[0,256])        
        v_peak = np.argmax(hist_v)
        # also get the saturation peak but from the left (i.e., highest s-value close to 0; max s-value is 50 (~ 20% of 255))
        hist_s = cv2.calcHist([s],[0],None,[256],[0,256])        
        s_peak = np.argmax(hist_s[0:50])        
        if v_peak < 255:
            a = np.arange(v_peak,256)
            w = np.squeeze(hist_v[v_peak:256])        
            # take average v-value beyond the v_peak        
            v_threshold = np.uint8(np.average(a, weights = w))                        
        else:
            v_threshold = v_peak
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            plt.figure("h image before");plt.imshow(h, cmap='gray') 
            plt.figure("s image");plt.imshow(s, cmap='gray')            
            plt.figure("v image");plt.imshow(v, cmap='gray') 
            plt.figure("histogram of v");plt.hist(v.ravel(),256,density=True)
            plt.figure("histogram of s");plt.hist(s.ravel(),256,density=True)
            plt.figure("histogram of h");plt.hist(h.ravel(),180,density=True)
            print('max of histogram of v = ', np.argmax(hist_v))
            b = np.arange(0,256)
            v_average = np.uint8(np.average(b, weights = np.squeeze(hist_v)))  
            v_std = np.uint8(np.sqrt(np.average((b - v_average)**2, weights = np.squeeze(hist_v))))                         
            print('s_peak value, v peak value, prob. mass beyond the peak, average beyond peak, v average, v_std ',s_peak, v_peak, sum(hist_v[v_peak:255])/sum(hist_v), v_threshold, v_average, v_std)
        
        # Whereever saturation <= s_peak  and v-value is around v_threshold, 
        # replace hue values x in the range [0,10] by 179-x 
        # find the v-value which is atleast > 1/8 of the max peak. This is because we assume that the 
        # background paper is at least > 1/8 of the total image area. And if the background paper is the "lightest"
        # then it must have a "reasonable" peak around the max peak (perhaps it is the max peak or it is just to its right) 
        # sensitivity of 15 for s-value and 10 for v-value
        h[(s <= s_peak+15) & (h <= 10) & (v >= v_threshold-10)] = 179 - h[(s <= s_peak+15) & (h <= 10) & (v >= v_threshold-10)]
        
        # binary segmentation on the h image
        ret,th = cv2.threshold(h,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':            
            plt.figure("h image after");plt.imshow(h, cmap='gray')                        
                
        # "opening" transformation
        # kernel for "opening" transformation 
        kernel_open = np.ones((5,5),np.uint8)
        img_for_bkgrnd = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open) 
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            plt.figure("segmentation/thresholding h-image");plt.imshow(img_for_bkgrnd, cmap='gray')
        
        # let us check if h-image thresholding gave us a reasonable background paper or not
        # check the area of the max_cntr, if it is less than 1/8 of the paper, try the saturation image as last resort
        major = cv2.__version__.split('.')[0]
        if major == '3':       
            im2, cnts, hier = cv2.findContours(img_for_bkgrnd,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        else:
            cnts, hier = cv2.findContours(img_for_bkgrnd,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cnts = sorted(cnts, key = cntrarea, reverse = True)[:5]
        
        # if nothing was found in previous segmentation (which basically used the v-value)
        # AND if nothing was found in the h-image
        # as a last resort try the s-image
        if (len(cnts) > 0 and cntrarea(cnts[0]) < 1/16*sh_img[0]*sh_img[1]) or len(cnts) == 0:            
            # use saturation image thresholding
            # binary segmentation on the h image
            # use cv2.THRESH_BINARY_INV because we want low saturation to appear bright
            ret,th = cv2.threshold(s,0,255,cv2.THRESH_BINARY_INV+cv2.THRESH_OTSU)
            # "opening" transformation
            # kernel for "opening" transformation 
            kernel_open = np.ones((5,5),np.uint8)
            img_for_bkgrnd = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open) 
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                plt.figure("segmentation/thresholding s-image");plt.imshow(img_for_bkgrnd, cmap='gray')
                        
        
    image = img_for_bkgrnd
    
    # the paper might not be parallel to the axis of the image 
    # use findContours to get the background paper 
    major = cv2.__version__.split('.')[0]
    if major == '3':       
        im2, cnts, hier = cv2.findContours(image,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        cnts, hier = cv2.findContours(image,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrarea, reverse = True)[:5]
        
    # blank image to draw the big contour which most likely represents the background paper
    img_bl =  np.zeros((sh_img[0],sh_img[1]),dtype = "uint8")
    bkgrnd_box = []
    # check if cnts is empty or not, if yes, then just take max_cntr to be the 640x480 box
    # this means our segmentation gave no box and perhaps there are issues with the photo
    if len(cnts) > 0:
        max_cntr = cnts[0]
    else:
        max_cntr = np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]])

    # check if tool(s) cut the background paper because tool might be big, still inside the paper, but casting shadows etc. 
    # by checking if max_cntr and second_max_cntr are of similar size.
    # If yes, then reduce the 'opening' kernel threshold to 3x3 and check if we get a background_box which 
    # has total area close to area of (max_cntr + second_max_cntr)
    # do this only in phase1 (if floor/table is low contrast then mostly this scenario will not occur)
    # also signal an ERROR_MESSAGE in this case as well
    if USE_SEGMENTATION == True:
        # only proceed if there are >= 2 contours
        if len(cnts) >= 2:
            # get the two max contours
            max_cntr = cnts[0]
            second_max_cntr = cnts[1]
            # their areas
            area_second_max_cntr = cntrarea(second_max_cntr)
            area_max_cntr = cntrarea(max_cntr)
            # sum of the areas
            tot_area_of_two_cntrs = area_max_cntr + area_second_max_cntr
            # proceed only if the second_max_cntr has some reasonable area compared to max_cntr
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print('area_second_max_cntr, area_max_cntr = ', area_second_max_cntr, area_max_cntr)
            # set the ratio of area_max_cntr and area_second_max_cntr for later use (in case "Photo too blurry" error)
            if (area_second_max_cntr >= 1/5*area_max_cntr):                 
                RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR = area_second_max_cntr/area_max_cntr
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print('RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR  = ', RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR )
            if (area_second_max_cntr >= 1/3*tot_area_of_two_cntrs):                 
                # merge the two contours by concatenating them vertically
                merged_max_secondmax_cntr = np.vstack((max_cntr,second_max_cntr))                
                # merged_area = cntrarea(merged_max_secondmax_cntr)
                # proceed for further processing only if convexhull area of the merged contour
                # is comparable to the sum of the two areas
                # the reason we use convexhull rahter than bounding box is that we haven't yet corrected for tilt
                # so by taking bounding box we would lose that information and do things incorrectly
                hull_merged_cntrs = cv2.convexHull(merged_max_secondmax_cntr) 
                area_hull_merged_cntrs = cntrarea(hull_merged_cntrs) 
                # make the threshold stricter now to 1/4 
                # we want the convex hull area not to be too large than the sum of the area of cntrs 
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print('area of hull_merged, tot_area_of_two_cntrs = ', area_hull_merged_cntrs, tot_area_of_two_cntrs)
                if  (np.abs(area_hull_merged_cntrs - tot_area_of_two_cntrs) <= 1/4*(tot_area_of_two_cntrs)):                                        
                    #max_cntr = merged_max_secondmax_cntr
                    # use a smaller 'opening' kernel threshold -- reduce kernel size from 7x7 to 3x3                    
                    kernel_open_smaller = np.ones((3,3),np.uint8)
                    img_for_bkgrnd_smaller = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open_smaller)
                    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                        figtitle = "segmentation/thresholding, kernel = "+str(3)
                        plt.figure(figtitle);plt.imshow(img_for_bkgrnd_smaller, cmap='gray')
                    # find the contours on this new image
                    major = cv2.__version__.split('.')[0]
                    if major == '3':       
                        im2, cnts, hier = cv2.findContours(img_for_bkgrnd_smaller,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    else:
                        cnts, hier = cv2.findContours(img_for_bkgrnd_smaller,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    cnts = sorted(cnts, key = cntrarea, reverse = True)[:5]                    
                    if len(cnts) > 0:
                        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                            print('new max_cntr area, area_hull_merged_cntrs = ',cntrarea(cnts[0]),area_hull_merged_cntrs)
                        # if the area of this current max_cntr is comparable to the merged_area then use this
                        # otherwise just use what we had obtained before for max_cntr                            
                        if np.abs(cntrarea(cnts[0]) - area_hull_merged_cntrs) <= 1/4*area_hull_merged_cntrs:                            
                            max_cntr = cnts[0]
                    else:
                        # if we found no cntr then something really might be off, just use 640x480
                        max_cntr = np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]])                    
                    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                        print('max_cntr area, second_max_cntr_area, merged_cntr_area = ', area_max_cntr, area_second_max_cntr,tot_area_of_two_cntrs)

    # get the temporary bkgrnd_box and draw the contour on a blank image for further processing
    # epsilon is the max distance from the original contour
    # we keep this to 5% from the arc_length of the original contour
    # this helps get rid of weird artifacts (like a small edge jutting out because of bad floor)
    epsilon = 0.05*cv2.arcLength(max_cntr,True)
    approx = cv2.approxPolyDP(max_cntr,epsilon,True)
    rect = cv2.minAreaRect(approx)
    box = cv2.boxPoints(rect)
    box = np.int0(box) 

    # do some checking on this bkgrnd_box, if too bad then just take the bkgrnd_box to be 648x480
    # check 1: if the bkgrnd_box area is too small, then just use the 640x480, we will mostly give "Zoom in?" error_message    
    if get_area(box) < 1/16*sh_img[0]*sh_img[1]:
        box = np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]])
    # check 2: if any corner point of bkgrnd_box is at some threshold distance outside the image boundary
    #          then again revert to 648x480, must be some issue with the photo
    if (np.min(box[:,0]) < -1*sh_img[1]/20) or (np.max(box[:,0]) >= sh_img[1]+sh_img[1]/20) or  (np.min(box[:,1]) < -1*sh_img[0]/20) or (np.max(box[:,1]) > sh_img[0]+sh_img[0]/20):
        box = np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]])    
    
    # use the corner points of the contour rather than the background box
    # these corner points can be got by the intersection of lines in lines_seg
    bkgrnd_box.append(box) # this bkgrnd_box is used in case of the edge detection process gives < 4 lines

    # use the max_cntr instead of approx contour got above to get the lines_seg because we don't want to lose information
    cv2.drawContours(img_bl,[max_cntr],0,(255,255,255),4)
    # use Hough lines on the binary image with only the max contour
    lines_seg = cv2.HoughLines(img_bl,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/3.0))    
    if lines_seg is not None:        
        lines_seg = np.squeeze(lines_seg)            
        # if the number of lines_seg is more than 200, then we have too many lines and perhaps 
        # the floor isn't that good wrt to the paper. So try to approx the max countour. This can
        # perhaps help "denoise"
        if len(lines_seg) >= 200: 
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                print('TOO MANY LINES!!!!!!!!!!!!!!!!!!!!!!!', len(lines_seg))
            # perhaps try to use the approx instead of max contour
            img_bl =  np.zeros((sh_img[0],sh_img[1]),dtype = "uint8")
            cv2.drawContours(img_bl,[approx],0,(255,255,255),4)
    
    # ERROR_DETECTION: phone far away from background paper
    # if the area of the max_cntr is much smaller than 640x480
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print("ratio of max_cntr area to total image area:= ", cntrarea(max_cntr)/(img_bl.shape[0]*img_bl.shape[1]))
    
    if cntrarea(max_cntr)/(img_bl.shape[0]*img_bl.shape[1]) <= 0.125:
        if ERROR_MESSAGE.find("Zoom in") == -1:
            ERROR_MESSAGE += "Zoom in. "
            ERROR_THREAT = "red"
        
    # ERROR_DETECTION: Shadow?
    # if the area of the max_cntr is smaller than its bounding box, then perhaps there was shadow cast on the paper
    if cntrarea(max_cntr)/get_area(box) < 0.8:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print("max_cntr area vs its bounding box area: ", cntrarea(max_cntr), get_area(box), cntrarea(max_cntr)/get_area(box))
        if ERROR_MESSAGE.find("Shadow") == -1:
            ERROR_MESSAGE += "Shadow. "
            ERROR_THREAT = "red"
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        figtitle = "background box obtained from segmentation "+str(USE_SEGMENTATION)
        plt.figure(figtitle);plt.imshow(img_bl, cmap='gray')
    
    # use Hough lines on the binary image with only the contour
    lines_seg = cv2.HoughLines(img_bl,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/3.0))    
    if lines_seg is not None:        
        lines_seg = np.squeeze(lines_seg)
        
    if DEBUG == True and MYENVIRONMENT == 'PYTHON': 
        if lines_seg is not None:           
            img_copy = np.copy(img)
            plot_lines(img_copy,lines_seg)
            plt.figure("hough lines obtained on previous black and white image");plt.imshow(img_copy, cmap='gray')        
            img_copy = np.copy(img)
            cv2.drawContours(img_copy, bkgrnd_box, -1, (255,0,255), 2)
            plt.figure("background box obtained from segmentation on original image");plt.imshow(img_copy, cmap='gray')
        
    # bkgrnd box from segmentation process for backup
    bkgrnd_box_seg = np.copy(bkgrnd_box)
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('bkgrnd_box_seg (backup): ', bkgrnd_box_seg)
    
    ##############################################################################################
    ################### Get Hough lines from the Edge detection process ##########################
    ##############################################################################################
    
    # get the Hough lines for the edge detected image
    blur_size = [1,3,5,7]
    hough_threshold = [4,5,6]
    # use blur_size = 3 and hough_threshold = 4 to start with
    lines_edge = get_lines_edge_image(gray, blur_size[1], hough_threshold[0], sh_img)    
    # sometimes even if we get > 4 lines, they need not be the ones around the paper;
    # it could just be clustering of multiple lines aroudn one line
    # in this case, we need to check first that we have existence of 4 lines around paper
    # if not then we need to reduce blur and/or decrease hough_threshold    
    if lines_edge is not None:            
        distinct_lines = len(lines_edge)
    else:
        distinct_lines = 0
        
    if distinct_lines > 4 and distinct_lines < 50: # if num of lines > 50, then perhaps we assume that we have those lines we want
        distinct_lines = get_num_distinct_lines(lines_edge)
    
    # sometimes background color is close to white paper, then we need to decrease blur to 1 
    # and reduce Hough threshold to sh_img[1]/5 to make sure we get our desired lines  
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        #print(lines_edge)
        print('distinct lines %d' % distinct_lines)
    if distinct_lines < 4:
        # make blur_size = 1        
        for j in range(0,len(hough_threshold)):
            lines_edge = get_lines_edge_image(gray, blur_size[0], hough_threshold[j], sh_img)
            if lines_edge is not None:
                distinct_lines = len(lines_edge)
            else:
                distinct_lines = 0
            if distinct_lines > 4 and distinct_lines < 50: # if num of lines > 50, then perhaps we assume that we have those lines we want
                distinct_lines = get_num_distinct_lines(lines_edge)
            if distinct_lines >= 4:
                break

    # sometimes because of carpet or hardwood or tiles we might get thousands of undesired lines in the edge detection
    # because blurring isn't too large. In such cases when num of lines_edge is > 1000 
    # increase the blurring to first 5, if it is still large > 1000, increase it to 7. 
    # we keep the hough_threshold fixed to 4 so that we are being strict with the lines we want
    elif distinct_lines >= 1000:        
        for i in range(2,len(blur_size)):
            lines_edge = get_lines_edge_image(gray, blur_size[i], hough_threshold[0], sh_img)
            if lines_edge is not None:
                number_lines_edge = len(lines_edge)
            else:
                number_lines_edge = 0
            if number_lines_edge < 1000:
                break            
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        img_copy = np.copy(img)
        #print(lines_edge)
        if lines_edge is not None:
            plot_lines(img_copy,lines_edge)
            print('number of lines_edge: %d' % len(lines_edge))
            plt.figure("hough lines on edge-detection image");plt.imshow(img_copy, cmap='gray')    
        else:
            print('lines_edge is EMPTY, perhaps no contrast with floor!')
        #print('lines from segmentation')
        #print(lines_seg)        
        #print('lines from edge detection')
        #print(lines_edge)
    
    # get the intersection of Hough lines obtained by the two methods above: lines_seg and lines_edge
    lines = []
    if lines_seg is not None and lines_edge is not None:
        line_count = 0        
        for l in lines_edge:
            # check if we have a new line_edge        
            if is_contained_in_lines_seg(l, lines_seg, sh_img, 0.02) and not (is_contained_in_lines_seg(l, lines, sh_img, 0.1)): 
                lines.append(l)
                line_count += 1
        
    # convert to list of numbers
    if (len(lines) > 0):
        lines = np.asarray(lines,dtype=np.float32)
    else:
        line_count = 0
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('intersected lines')
        print(lines)
        img_copy = np.copy(img)
        plot_lines(img_copy,lines)
        plt.figure("intersection of lines_seg and lines_edge");plt.imshow(img_copy, cmap='gray')
        
    if line_count < 4:
        print('Some sing is wong!!! Less than four lines (%d) found!!! Using the bkgrnd_box found in segmentation!' % line_count)
        # ERROR_DETECTION: Background paper was not properly detected.
        # check if the hough line detection gave exactly 4 lines; if not then background paper was not detected properly and 
        # fall back upon an older background box
        if ERROR_MESSAGE.find("Background paper was not properly detected") == -1:
            ERROR_MESSAGE += "Background paper was not properly detected. "
            ERROR_THREAT = "red"        
        bkgrnd_box = bkgrnd_box_seg
        # we found zero or one line in the intersection, then perhaps the photo is blurry or there is no contrast with the floor        
        if line_count <= 1:
            if ERROR_MESSAGE.find("No contrast with floor/table or blurry photo") == -1:
                ERROR_MESSAGE += "No contrast with floor/table or blurry photo. "
        else:
             # lines are still < 4, but check whether bkgrnd_box from segmentation is out of the image
             a = np.min(bkgrnd_box[0], axis=0)
             b = np.max(bkgrnd_box[0], axis=0)
             xmin = a[0]
             ymin = a[1]
             xmax = b[0]
             ymax = b[1]
             # if any is close to the boundary, signal error
             distance = np.min([xmin,ymin,sh_img[1]-xmax,sh_img[0]-ymax])             
             # assume paper is 80% of the image?
             if distance < 0.8*(640/297)*5:
                 if ERROR_MESSAGE.find("Background paper out of image") == -1:
                     ERROR_MESSAGE += "Background paper out of image. "
             
    elif line_count > 4:        
        bkgrnd_box = []
        # check if we can refine to get the four lines of the background paper        
        # get the list of indices for lines
        lines_ind = list(range(0,len(lines)))
        # now go through all the 4 combinations
        candidate_rects = []
        candidate_areas = []
        for subset in itertools.combinations(lines_ind, 4):
            flag = True
            #print(subset)
            # set the flag for the "true" four lines to be True at the start
            # get the four lines
            line1 = lines[subset[0]]; line2 = lines[subset[1]]; line3 = lines[subset[2]]; line4 = lines[subset[3]]
            # check if each line intersects with only two other lines inside the image
            a = intersectWithTwoLines(line1, [line2, line3, line4], sh_img)
            b = intersectWithTwoLines(line2, [line1, line3, line4], sh_img)
            c = intersectWithTwoLines(line3, [line1, line2, line4], sh_img)
            d = intersectWithTwoLines(line4, [line1, line2, line3], sh_img)
            #print("ABCD", a,b,c,d)
            if a!=2 or b!=2 or c!=2 or d!=2: # check if there are two intersection points inside the image
                flag = False                
            # if lines are valid, then compute the area
            if(flag == True):
                # we have perhaps a valid set of four lines
                # find the points of intersection and compute the area
                temp_rect = []
                for i in range(0,len(subset)-1):         
                    for j in range(i+1,len(subset)):
                        line1 = lines[subset[i]]; line2 = lines[subset[j]] 
                        # make sure they aren't parallel lines
                        if (np.abs(line1[1]-line2[1])) > 1e-4: 
                            # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
                            if (np.abs(1/np.tan(line1[1])-1/np.tan(line2[1]))) > 1e-4: 
                                a = intersection(line1, line2)    
                                if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):                         
                                    temp_rect.append(a)
                temp_rect = np.asarray(temp_rect,dtype=np.int64)
                tr = []; tr.append(temp_rect); temp_rect = tr
                # order the corners in bkgrnd_box
                if (np.linalg.norm(temp_rect[0][0] - temp_rect[0][1]) >= np.linalg.norm(temp_rect[0][0] - temp_rect[0][2])):
                    temp_pt = np.copy(temp_rect[0][1])
                    temp_rect[0][1] = temp_rect[0][2]
                    temp_rect[0][2] = temp_pt
                if (np.linalg.norm(temp_rect[0][1] - temp_rect[0][2]) >= np.linalg.norm(temp_rect[0][1] - temp_rect[0][3])):
                    temp_pt = np.copy(temp_rect[0][2])
                    temp_rect[0][2] = temp_rect[0][3]
                    temp_rect[0][3] = temp_pt
                # take the area of the candidate rectangle
                area1 = np.abs(np.cross(temp_rect[0][0] - temp_rect[0][1], temp_rect[0][0] - temp_rect[0][3]))/2.0 
                area2 = np.abs(np.cross(temp_rect[0][2] - temp_rect[0][1], temp_rect[0][2] - temp_rect[0][3]))/2.0 
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print(subset, area1+area2, temp_rect)
                candidate_rects.append(temp_rect)
                candidate_areas.append(area1+area2)
        # choose the largest area 
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print(candidate_areas, len(candidate_areas))
            print(candidate_rects, len(candidate_rects))
        # make sure there is at least one canditate rectangle, then sort and pick the largest rectangle
        if len(candidate_rects) > 0:
            a = sorted(range(len(candidate_areas)), key=lambda k: candidate_areas[k])
            bkgrnd_box = candidate_rects[a[len(a)-1]]
            # check if there is another candidate rectangle of comparable size which might be a "better" candidate
            if len(a) >= 2:
                bkgrnd_box_second = candidate_rects[a[len(a)-2]]
                # compare area of bkgrnd_box_second and bkgrnd_box
                # if at least 80% of the largest box then process further
                # we have to take contour area because the boxes have not yet been perspective transformed
                area1 = cntrarea(np.reshape(np.squeeze(bkgrnd_box), (np.squeeze(bkgrnd_box).shape[0],1,np.squeeze(bkgrnd_box).shape[1])))
                area2 = cntrarea(np.reshape(np.squeeze(bkgrnd_box_second), (np.squeeze(bkgrnd_box_second).shape[0],1,np.squeeze(bkgrnd_box_second).shape[1])))
                # proceeed only if area2 is at least 75% of area1
                if  area2 >= 0.80*area1:
                    # ratio of bkrgnd_box to its minimum bounding box                    
                    rect1 = cv2.minAreaRect(np.reshape(np.squeeze(bkgrnd_box), (np.squeeze(bkgrnd_box).shape[0],1,np.squeeze(bkgrnd_box).shape[1])))
                    box1 = cv2.boxPoints(rect1)
                    box1 = np.int0(box1) 
                    ratio1 = area1/get_area(box1)
                    # ratio of bkrgnd_box_second to its minimum bounding box
                    rect2 = cv2.minAreaRect(np.reshape(np.squeeze(bkgrnd_box_second), (np.squeeze(bkgrnd_box_second).shape[0],1,np.squeeze(bkgrnd_box_second).shape[1])))
                    box2 = cv2.boxPoints(rect2)
                    box2 = np.int0(box2) 
                    ratio2 = area2/get_area(box2)
                    # ratio of the second bkgrnd box is closer to 1 then perhaps it is a better candidate
                    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                        print('ratio1, ratio2, area1, box1, area2, box2  = ',np.squeeze(bkgrnd_box), np.squeeze(bkgrnd_box_second), ratio1, ratio2, area1, get_area(box1), area2, get_area(box2))
                    if np.abs(ratio2-1.0) < np.abs(ratio1-1.0):
                        bkgrnd_box = bkgrnd_box_second
        else:
            # ERROR_DETECTION: Background paper out of image?
            # check if any corner lies outside the image boundary
            if ERROR_MESSAGE.find("Background paper out of image") == -1:
                ERROR_MESSAGE += "Background paper out of image. "
                ERROR_THREAT = "red"
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('bkgrnd_box when > 4 lines')
            print(bkgrnd_box)
    else:         
        bkgrnd_box = []             
        for i in range(0,len(lines)-1):         
            for j in range(i+1,len(lines)):
                line1 = lines[i]; line2 = lines[j]
                # make sure they aren't parallel lines
                if np.abs(line1[1] - line2[1]) > 1e-4: 
                    # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
                    if (np.abs(1/np.tan(line1[1])-1/np.tan(line2[1]))) > 1e-4: 
                        a = intersection(line1, line2)
                        #print('pts of intersection: ', a)
                        if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):
                            bkgrnd_box.append(a)    
        #print('bkgrnd_box', bkgrnd_box)
        # convert to list of numbers
        if (len(bkgrnd_box) > 0):
            a = np.asarray(bkgrnd_box,dtype=np.int64)
            b = []
            b.append(a)
            bkgrnd_box = b     
        
        # we still need to check that we indeed get a reasonable quadrilateral
        # for example, it could happen that we get three colinear points 
        if (check_quadrilateral(lines, sh_img) == False):
            # set to empty and take care later
            bkgrnd_box = []
            # make sure to flag an error since there must have been some issue with the backgroud paper
            if ERROR_MESSAGE.find("Background paper was not properly detected") == -1:
                ERROR_MESSAGE += "Background paper was not properly detected. "
                ERROR_THREAT = "red"
        
        # note that in the above we take intersection of the 4 lines
        # this might result in a corner point lying outside the image 
        # because originally the paper was outside the image view
        # so we have to take care of the fact that we might get only <= 3 corner points

        # order the corners in bkgrnd_box only if there are 4 sides
        # if not then it is taken care of later 
        if len(bkgrnd_box) > 0:
            if len(bkgrnd_box[0]) == 4:            
                if (np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][1]) >= np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][2])):
                    temp_pt = np.copy(bkgrnd_box[0][1])
                    bkgrnd_box[0][1] = bkgrnd_box[0][2]
                    bkgrnd_box[0][2] = temp_pt
                if (np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][2]) >= np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][3])):
                    temp_pt = np.copy(bkgrnd_box[0][2])
                    bkgrnd_box[0][2] = bkgrnd_box[0][3]
                    bkgrnd_box[0][3] = temp_pt  
            elif len(bkgrnd_box[0]) < 4:
                # ERROR_DETECTION: Background paper out of image?
                # check if any corner lies outside the image boundary
                if ERROR_MESSAGE.find("Background paper out of image") == -1:
                    ERROR_MESSAGE += "Background paper out of image. "
                    ERROR_THREAT = "red"
        else:
            # we couldn't find background paper
            if ERROR_MESSAGE.find("Background paper out of image") == -1:
                ERROR_MESSAGE += "Background paper out of image. "
                ERROR_THREAT = "red"
        
    if len(bkgrnd_box) > 0: # make sure there are 4 corners
        if len(bkgrnd_box[0]) < 4 or len(bkgrnd_box[0]) > 4:
            if len(bkgrnd_box[0]) > 4 and ERROR_MESSAGE.find("Background paper was not properly detected") == -1:
                ERROR_MESSAGE += "Background paper was not properly detected. "
                ERROR_THREAT = "red"                               
            # if bkgrnd_box doesn't have corners then revert back to the one obtained from segmentation if it has four corners
            if len(bkgrnd_box_seg) > 0:
                if len(bkgrnd_box_seg[0]) == 4:    
                    bkgrnd_box = bkgrnd_box_seg
                else:
                    print('No background paper found anywhere :- 1 !!!')
                    # just use the entire 640x480 as background box in this case
                    bkgrnd_box = []
                    bkgrnd_box.append(np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]]))
            else:
                print('No background paper found anywhere :- 2 !!!')
                # just use the entire 640x480 as background box in this case
                bkgrnd_box = []
                bkgrnd_box.append(np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]]))                
        else:            
            # just check if we have signalled ERROR_MESSAGE = Shadow. This might have happened because the segmentation image
            # had some defects due to the floor or something else and Shadows was not really the problem at this junture 
            # because we have found 4 lines in the intersection of lines_seg and lines_edge. So perhaps "Shadow?" is wrongly signalled
            # we just remove it
            if ERROR_MESSAGE.find("Shadow") != -1 and line_count >= 4:                        
                ERROR_MESSAGE = ERROR_MESSAGE.replace("Shadow. ", "") 
                if ERROR_MESSAGE == "":
                    ERROR_THREAT = ""
    else: # if bkgrnd_box obtained from intersection of edge and segmented image gave nothing then revert to the previous one
        if len(bkgrnd_box_seg) > 0:            
            if len(bkgrnd_box_seg[0]) == 4:    
                bkgrnd_box = bkgrnd_box_seg                
            else:
                print('No background paper found anywhere :-3 !!!')
                # just use the entire 640x480 as background box in this case
                bkgrnd_box = []
                bkgrnd_box.append(np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]]))
        else:
            print('No background paper found anywhere :-4 !!!')
            # just use the entire 640x480 as background box in this case
            bkgrnd_box = []
            bkgrnd_box.append(np.int32([[0,0], [0,sh_img[0]-1], [sh_img[1]-1,sh_img[0]-1], [sh_img[1]-1,0]]))
        
    return bkgrnd_box

# get the distance of the contour from the image boundary
def distance_of_contour_to_boundary(cntr, sh_img):    
    cntr = np.squeeze(cntr)     
    #print('max of cntr is:= ', a[0],a[1])
    xymax = np.amax(cntr,axis=0)
    xmax = xymax[0]
    ymax = xymax[1]
    xymin = np.amin(cntr,axis=0)
    xmin = xymin[0]
    ymin = xymin[1]    
    distance = np.min([xmin,ymin,sh_img[1]-xmax,sh_img[0]-ymax])
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print("xmin, xmax, ymin, ymax, mindist ", xmin, xmax, ymin, ymax, distance)
    return distance

# return things to the javascript tool
def data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, flag_scale):

    global ERROR_MESSAGE
    global ERROR_THREAT
    global RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR
    
    final_number_of_tools = 0
    
    if len(pruned_cntrs) == 0:
        print('Some sing is wong!!! Zero contours found!!!')
        # return empty sets/numbers
        ERROR_MESSAGE += "Photo too blurry or tool has no contrast with paper. "
        # check here if we get only the background paper because
        # the background paper could have been split by a big tool
        if RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR < 1:
            if ERROR_MESSAGE.find("Background paper was not properly detected") == -1:
                ERROR_MESSAGE += "Background paper was not properly detected. "
            if ERROR_MESSAGE.find("Tool too close to paper boundary or extends beyond it") == -1:
                ERROR_MESSAGE += "Tool too close to paper boundary or extends beyond it. "            
                
        return [],[],[],-1,-1
    else:        
        
        myhulls = []
        myrawdata = []
        myoffsetX = []
        myoffsetY = []   
        myprunedCntrs= []
        for c in pruned_cntrs: 
            rect = cv2.minAreaRect(c)            
            box = cv2.boxPoints(rect)
            box = np.int0(box)
            # if the outline is such that its contour area is much smaller than the bounding box area
            # then most likely this is a long spurious edge and we should just throw it away         
            # OR is contour area is less than 25mm^2 then throw it away (assume 1 mm = 640/297 pixels)
            area_of_contour = cntrarea(c)
            if (area_of_contour/get_area(box) < THRESH_CONTOURAREA_BOXAREA) or area_of_contour < (640/297*5)*(640/297*5):
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':                    
                    print('eliminated one contour because either its contour area is much smaller than the bounding box area OR itself has small area! ', cntrarea(c), get_area(box))
                continue
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                moments = cv2.moments(c)
                centroid_x = int(moments['m10']/moments['m00'])
                centroid_y = int(moments['m01']/moments['m00'])
                print('area of contour, perimeter of contour, area of contour/area of bounding box, centroid  %.2f %.2f %.2f (%.2f, %.2f)' % (cntrarea(c), cntrperi(c), cntrarea(c)/get_area(box), centroid_x, centroid_y))
                
            
            # check for spurious tools at the boundary     
            # first check if the contour has area < 2.25 cm^2 OR if the background paper was not properly detected
            if (area_of_contour < (640/297*15)*(640/297*15) or ERROR_MESSAGE.find("Background paper was not properly detected") != -1):
                # get the moments and compute the centroid
                moments = cv2.moments(c)
                centroid_x = int(moments['m10']/moments['m00'])
                centroid_y = int(moments['m01']/moments['m00'])
                # if centroid is too close to border then just continue to next contour; this contour is most likely spurious
                # threshold we use is 5mm from any border -- 1mm = 640/297 pixels                
                if (np.abs(centroid_x-0) < 5*640/297 or np.abs(centroid_x-sh_img[1]) < 5*640/297 or np.abs(centroid_y-0) < 5*640/297 or np.abs(centroid_y-sh_img[0]) < 5*640/297):
                    continue
                # make it slightly stricter if ERROR_MESSAGE contains "Background paper was not properly detected."
                if (ERROR_MESSAGE.find("Background paper was not properly detected") != -1 and (np.abs(centroid_x-0) < 7*640/297 or np.abs(centroid_x-sh_img[1]) < 7*640/297 or np.abs(centroid_y-0) < 7*640/297 or np.abs(centroid_y-sh_img[0]) < 7*640/297)):
                    continue
            
            # count number of tools
            final_number_of_tools += 1
                                    
            # get the convex hull
            myprunedCntrs.append(c)
            hull = cv2.convexHull(c)              
                        
            # ERROR_DETECTION: Tool too close or extends beyond paper boundary? 
            # Check: If the closest point of any tool path is closer than 1mm from the white paper boundary.
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                print("tool distance from boundary:= ", distance_of_contour_to_boundary(c, sh_img))
            if distance_of_contour_to_boundary(c, sh_img) <= np.ceil((640/297)*1):                
                if ERROR_MESSAGE.find("Tool too close to paper boundary or extends beyond it") == -1:
                    ERROR_MESSAGE += "Tool too close to paper boundary or extends beyond it. "                    
                ERROR_THREAT = "red"
                # check if we had an ERROR_MESSAGE = Shadow?. Because perhaps we signalled it wrongly because tool
                # was too large and previous criteria signalled Shadow?. 
                # If the tool split the background paper into two, then "Tool too close..." should be
                # the error message and perhaps we shouldn't signal Shadow?
                if ERROR_MESSAGE.find("Shadow") != -1:                        
                    ERROR_MESSAGE = ERROR_MESSAGE.replace("Shadow. ", "")                
                    if ERROR_MESSAGE == "":
                        ERROR_THREAT = ""            
            # draw the contours and hulls in an image to make sure things are going okay
            # note that this image might be distorted since we have done perspective transform 
            # of the backgournd paper to the width/height ratio of the original image. But this
            # is OK since in Javascript we do the correct scaling.
            if ERROR_THREAT == "red":
                cv2.drawContours(transformed_img_with_cntrs_hulls, [c], -1, (0,165,255), 4)             
            else:
                cv2.drawContours(transformed_img_with_cntrs_hulls, [c], -1, (0,255,0), 4) 
            #cv2.drawContours(transformed_img_with_cntrs_hulls, [hull], -1, (255,0,255), 2)
            if MYENVIRONMENT == 'PYTHON':
                cv2.drawContours(transformed_img_with_cntrs_hulls, [hull], -1, (255,0,255), 2)
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':                
                plt.figure("final tool outlines and convexhulls");plt.imshow(transformed_img_with_cntrs_hulls, cmap='gray')
            
            points = np.squeeze(c)
            points = points.astype(float)
            x_min = np.min(points[:,0])
            y_min = np.min(points[:,1])
            hull_points = np.squeeze(hull)
            hull_points = hull_points.astype(float)
            
            # scale to the A4 paper size and flatten them so that we output a 1-dim array
            cntr_pts = []
            hull_pts = []
            for i in range(0,len(points[:,0])):
                # if flag is set to true scale to A4 size (for main ToolKaiser)
                if flag_scale == True:
                    points[i,0] = round(float(points[i,0] - x_min)*float(A4_w/sh_img[1]),2)
                    points[i,1] = round(float(points[i,1] - y_min)*float(A4_h/sh_img[0]),2)
                # else scale things by a factor of 10 to keep tools reasonable; used for processing images from web crawling
                else:
                    points[i,0] = round(float((points[i,0] - x_min)/10),2)
                    points[i,1] = round(float((points[i,1] - y_min)/10),2)
                # convert numpy values to naive Python types; this is needed while transmitting back to the client
                cntr_pts.append(points[i,0].item())
                cntr_pts.append(points[i,1].item())
            for i in range(0,len(hull_points[:,0])):  
                # if flag is set to true scale to A4 size (for main ToolKaiser)
                if flag_scale == True:
                    hull_points[i,0] = round(float(hull_points[i,0] - x_min)*float(A4_w/sh_img[1]),2)        
                    hull_points[i,1] = round(float(hull_points[i,1] - y_min)*float(A4_h/sh_img[0]),2)
                # else scale things by a factor of 10 to keep tools reasonable; used for processing images from web crawling
                else:
                    hull_points[i,0] = round(float((hull_points[i,0] - x_min)/10),2)        
                    hull_points[i,1] = round(float((hull_points[i,1] - y_min)/10),2)
                # convert numpy values to naive Python types; this is needed while transmitting back to the client
                hull_pts.append(hull_points[i,0].item())
                hull_pts.append(hull_points[i,1].item())
                
            # append the first point at the end to close the loop
            cntr_pts.append(points[0,0].item())
            cntr_pts.append(points[0,1].item())
            hull_pts.append(hull_points[0,0].item())
            hull_pts.append(hull_points[0,1].item())
            
            # create a list of the cntrs & hulls etc. if there are multiple objects in the image
            myrawdata.append(cntr_pts)
            myhulls.append(hull_pts)
            myoffsetX.append(x_min.item())
            myoffsetY.append(y_min.item())

        
        if MYENVIRONMENT == 'PYTHON':
            cv2.imwrite(os.path.join(folder_jpg_results,fname),transformed_img_with_cntrs_hulls)
        
        #print('length of various things:= ', len(mycntrs), len(myhulls), len(myoffsetX), len(myoffsetY))
        
        # ERROR_DETECION: not really error but just signal to the user that there are N number of tools found
        # Check: if at the end the number of tools > 1
        if final_number_of_tools > 1:
                #ERROR_MESSAGE += "%d tools? " % final_number_of_tools
                ERROR_MESSAGE += ""
        elif final_number_of_tools == 0: # if no tools are found after pruning then we declare error because photo must be blurry
            ERROR_MESSAGE += "Photo too blurry or tool has no contrast with paper. "
            # check here if we get only the background paper because
            # the background paper could have been split by a big tool
            if RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR < 1:
                if ERROR_MESSAGE.find("Background paper was not properly detected") == -1:
                    ERROR_MESSAGE += "Background paper was not properly detected. "
                if ERROR_MESSAGE.find("Tool too close to paper boundary or extends beyond it") == -1:
                    ERROR_MESSAGE += "Tool too close to paper boundary or extends beyond it. "                
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                print('No tools found at all!!!!!!!!!!!!!!!')
                
        return myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs    

# get the outline along the tool/object
def get_contours(im, fov, zoom_factor):

    ######### set error message to empty first ##########
    global ERROR_MESSAGE
    global ERROR_THREAT
    global USE_SEGMENTATION
    global RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR
    # reset all the global flags
    ERROR_MESSAGE = "" # default is empty -- no error
    ERROR_THREAT = ""    
    USE_SEGMENTATION = True
    RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR = 1
    
    if MYENVIRONMENT == 'PYTHON':
        print(' ', USE_SEGMENTATION, RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR)
    
    ##############################################################################################
    ###################  Get the image and convert to appropriate size ###########################
    ##############################################################################################
    
    a = im.shape[0]
    b = im.shape[1]
    
    if DEBUG == True:
        print("fov:=", fov)
    
    # if image in landscape convert to portrait
    if a <= b:    
        im = imutils.rotate_bound(im, 90)    
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("original image");plt.imshow(im, cmap='gray')
    
    # resize image to 640 rows for faster processing; low resolution also makes several CV algos perform better
    # note that the original image is scaled to 640 rows (and the cols are scaled to maintain the aspect ratio)    
    a_new = 640    
    img = cv2.resize(im,(int(a_new*im.shape[1]/im.shape[0]), a_new))    
    
    #img = cv2.resize(im,(a_new,int(a_new*b/a)))    
    sh_img = img.shape
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('original size:',im.shape[0],im.shape[1])
        print('size after resizing to 640 rows: ', sh_img[0], sh_img[1])
    
    # get the grayscale image
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
    ##############################################################################################
    ###################  Get the background paper first ##########################################
    ##############################################################################################
    
    bkgrnd_box = get_background_paper(gray,img,sh_img)
    print('ERROR_MESSAGE TILL NOW = ', ERROR_MESSAGE)
    # check if ERROR_MESSAGE contains "No contrast with floor" AND (doesn't contain "Zoom in" OR "Shadow")
    # we give "Zoom in" and "Shadow" preference and don't do the other processing if these two messages are present
    #if (ERROR_MESSAGE.find("No contrast with floor") != -1 and (ERROR_MESSAGE.find("Zoom in") == -1 and ERROR_MESSAGE.find("Shadow") == -1)):        
    if (ERROR_MESSAGE.find("No contrast with floor") != -1 and ERROR_MESSAGE.find("Zoom in") == -1):        
        # use the edge detection with very low values of thresholds for the Canny edge detector
        USE_SEGMENTATION = False
        ## reset the error values
        #ERROR_MESSAGE = "" 
        #ERROR_THREAT = ""
        bkgrnd_box = get_background_paper(gray,img,sh_img)
        
    
    ##### check if the background_box area is smaller than its minimum bounding box #####
    vector_a = bkgrnd_box[0][0] - bkgrnd_box[0][1]
    vector_b = bkgrnd_box[0][0] - bkgrnd_box[0][3]
    row1 = np.asarray([vector_a[0], vector_a[1]])
    row2 = np.asarray([vector_b[0], vector_b[1]])
    matrix = np.asarray([row1, row2])
    area_of_background_box1 = np.abs(np.linalg.det(matrix)/2.0)
    vector_a = bkgrnd_box[0][2] - bkgrnd_box[0][1]
    vector_b = bkgrnd_box[0][2] - bkgrnd_box[0][3]
    row1 = np.asarray([vector_a[0], vector_a[1]])
    row2 = np.asarray([vector_b[0], vector_b[1]])
    matrix = np.asarray([row1, row2])
    area_of_background_box2 = np.abs(np.linalg.det(matrix)/2.0)
    total_area_background_box = area_of_background_box1+area_of_background_box2
    # get area of the minimum bounding box
    rect_background_box = cv2.minAreaRect(np.asarray(bkgrnd_box[0]))
    box_background_box = cv2.boxPoints(rect_background_box)
    box_background_box = np.int0(box_background_box)
    area_minimum_bounding_box = get_area(box_background_box)
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('total_area_background_box', total_area_background_box)
        print('area_minimum_bounding_box', area_minimum_bounding_box)
        
    # ERROR_DETECTION: phone tilted
    # check if area of the quadrilateral of final background_box is smaller than the minimum bounding box
    if total_area_background_box/area_minimum_bounding_box < 0.9:
        ERROR_MESSAGE += "Phone tilted. (%.2f) " % (total_area_background_box/area_minimum_bounding_box)
        ERROR_THREAT = "red"
        
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('final background trapezoid')
        print(len(bkgrnd_box[0]), bkgrnd_box)
    
    if USE_HIGH_RES == True:
        # get the background box in the original image
        scale_factor = im.shape[0]/img.shape[0]
        bkgrnd_box_original_image = []
        bkgrnd_box_original_image.append(scale_factor*bkgrnd_box[0])
        # get the area of the background paper for height estimation in mm
        area_of_background_box_in_px = np.linalg.norm(bkgrnd_box_original_image[0][0] - bkgrnd_box_original_image[0][1])*np.linalg.norm(bkgrnd_box_original_image[0][1] - bkgrnd_box_original_image[0][2])
        # assume that the paper is A4 (297x210)
        one_px_in_mm = np.sqrt(297*210/area_of_background_box_in_px)
        # actual length of the image in mm
        length_of_image_in_mm = one_px_in_mm*np.maximum(im.shape[0],im.shape[1])
        # estimate the height of the camera from the floor; this is used to warn the user if we are too close
        # ERROR_DETECTION: Phone too close? (d cm)
        # estimate the distance of camera to the floor and if it smaller than 60 cm, then signal error
        estimated_distance_in_mm = length_of_image_in_mm*zoom_factor/(2.0*np.tan(fov*math.pi/360))
        if estimated_distance_in_mm < 600:
            ERROR_MESSAGE += "Phone too close. (%.0f cm) " % np.round(estimated_distance_in_mm/10)
            ERROR_THREAT  = "red"
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('final background trapezoid in original image')            
            print(len(bkgrnd_box_original_image[0]), bkgrnd_box_original_image)
            print('one_px_in_mm:= ', one_px_in_mm)
            print('length_of_image_in_mm:= ', length_of_image_in_mm)
            print('estimated_distance_in_mm', ERROR_MESSAGE)
        
    
    # apply the perspective transform to remove the shear/rotation etc.
    # note: we have scaled the img to 640 rows (and cols are scaled according to the aspect ratio of original image)
    # hence when we do the perspective transform and get the image filled with the backgroud paper, we are 
    # distorting the image slightly. The image filled with background paper should ideally have the aspect ratio
    # of the paper, but it has the aspect ration of the original image. This is not an issue since we scale the 
    # image appropriately in Javascript. We maintain the current scaling so that we don't lose on the image processing
    if USE_HIGH_RES == False:
        img_new, gray_new = get_perspective_transform(bkgrnd_box, sh_img, img, gray)
    else:
        # get the grayscale image
        gray_high_res = cv2.cvtColor(im, cv2.COLOR_BGR2GRAY)
        img_new, gray_new = get_perspective_transform(bkgrnd_box_original_image, sh_img, im, gray_high_res)
        
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("image after perspective transform");plt.imshow(img_new, cmap='gray')
    
    ##############################################################################################
    ################### Get Tool Outline #########################################################
    ##############################################################################################
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('############### Tool outline detection ####################')
    # histogram equalization
    # create a CLAHE object (Arguments are optional).
    if USE_HIST_EQUALIZATION == True:
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        gray_new = clahe.apply(gray_new)
    # a little Gaussian smoothing to remove spurious edges
    blur = cv2.GaussianBlur(gray_new,(3,3),0) 
    # compute the median of the single channel pixel intensities
    v = np.median(blur)
    # apply automatic Canny edge detection using the computed median
    # use larger SIGMA to get more edges
    lower = int(max(0, (1.0 - 0.75) * v))
    upper = int(min(255, (1.0 + 0.75) * v))
    #lower = 100
    #upper = 200
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('lower %d, upper %d for tool edge detection on transformed image' % (lower, upper))
    edges = cv2.Canny(gray_new,lower,upper)    
    # "closing" transformation        
    # kernel for "closing" transformation 
    kernel_close = np.ones((7,7),np.uint8)
    img_for_tool_outline_edge = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)       
    
    transformed_img = np.copy(img_new)
    transformed_img_with_cntrs_hulls = np.copy(img_new)
        
    # clean up the boundary a little bit
    img_for_tool_outline_edge = clean_up_boundary(img_for_tool_outline_edge)    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("image for tool 1 detection, perspective -> edge detection");plt.imshow(img_for_tool_outline_edge, cmap='gray')   
    
    # use the downsampled (640 rows reshaped) image to find the edges for tool detection
    # this might be better than the image obtained from perspective transformation 
    # because we lose resolution. Do the perspective transform on the edge image thus obtained
    # so the previous is perspective transform and then edge detection
    # below is edge detection and then perspective transform
    if USE_DOWNSAMPLED_IMAGE == True: 
        # histogram equalization
        # create a CLAHE object (Arguments are optional).
        if USE_HIST_EQUALIZATION == True:
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            gray = clahe.apply(gray)
        # a little Gaussian smoothing to remove spurious edges        
        blur = cv2.GaussianBlur(gray,(3,3),0)             
        # compute the median of the single channel pixel intensities
        v = np.median(blur)
        # apply automatic Canny edge detection using the computed median
        # use larger SIGMA to get more edges
        lower = int(max(0, (1.0 - 2*SIGMA) * v))
        upper = int(min(255, (1.0 + 2*SIGMA) * v))
        #lower = 100
        #upper = 200
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('lower %d, upper %d for tool edge detection on original image' % (lower, upper))
        
        edges = cv2.Canny(gray,lower,upper) 
        # apply the perspective transform to remove the shear/rotation etc.
        img_new_downsampled, edges_new_downsampled = get_perspective_transform(bkgrnd_box, sh_img, img, edges)       
        
        # edges_new might not be completely binary due to perspective transformation
        # we can do thresholding to make it completely binary, but not needed perhaps
        #edges_new[edges_new > 0] = 255
        
        # "closing" transformation        
        # kernel for "closing" transformation 
        # originially used was (7,7)
        kernel_close = np.ones((9,9),np.uint8)
        img_for_tool_outline_edge_downsampled = cv2.morphologyEx(edges_new_downsampled, cv2.MORPH_CLOSE, kernel_close)
        # clean up the boundary a little bit
        img_for_tool_outline_edge_downsampled = clean_up_boundary(img_for_tool_outline_edge_downsampled)
        
        # lets do some sanity checking -- whether we want to use this downsampled image or not
        pruned_cntrs = []
        temp_pruned_cntrs = get_pruned_cntrs(img_for_tool_outline_edge,img_new)
        if len(temp_pruned_cntrs) > 0:
            pruned_cntrs.append(temp_pruned_cntrs)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('pruned cntrs on the first approach: ', len(pruned_cntrs))
            if len(pruned_cntrs)>0:
                for c in pruned_cntrs:
                    print(cntrperi(c), cntrarea(c))   
        pruned_cntrs_downsampled = [] 
        temp_pruneds_cntrs_downsampled = get_pruned_cntrs(img_for_tool_outline_edge_downsampled,img_new_downsampled)
        if len(temp_pruneds_cntrs_downsampled) > 0:
            pruned_cntrs_downsampled.append(temp_pruneds_cntrs_downsampled)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('pruned cntrs on the second approach: ', len(pruned_cntrs_downsampled))
            if len(pruned_cntrs_downsampled)>0:
                for c in pruned_cntrs_downsampled:                    
                    print(cntrperi(c), cntrarea(c))
        
        # if there was no contour found in the previous process, use the downsampled image
        if len(pruned_cntrs) == 0:
            print('using downsampled image before perspective transform for tool edge detection...')            
            img_for_tool_outline_edge = img_for_tool_outline_edge_downsampled            
            pruned_cntrs = pruned_cntrs_downsampled                   
        elif len(pruned_cntrs_downsampled) > 0:            
            # if the length (perimeter) of the largest contour in the downsampled image is smaller than
            # that of the same in the previous approach, then use the downsampled image
            if cntrperi(pruned_cntrs_downsampled[0])/cntrperi(pruned_cntrs[0]) < 1.0:
                print('using downsampled image before perspective transform for tool edge detection...')            
                img_for_tool_outline_edge = img_for_tool_outline_edge_downsampled
                pruned_cntrs = pruned_cntrs_downsampled
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print('using downsampled image before perspective transform for tool edge detection...')            
                    plt.figure("image for tool 1 detection, edge detection -> perspective");plt.imshow(img_for_tool_outline_edge, cmap='gray')
        
    # use the information in the segmented image tocombine with edge image for tool outline
    # this is useful when shadows are cast and the edge image doesn't give information about the
    # tool outline. segmentation will include the shadow which we can perhaps easily edit in the tool editor
    if USE_SEGMENTATION_TOOL_OUTLINE == True:
        # work on the image before the perspective transformation
        ret,th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        #img_seg = cv2.bitwise_not(th)        
        # "opening" transformation
        # kernel for "opening" transformation 
        kernel_open = np.ones((7,7),np.uint8)
        img_for_tool_outline_seg = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open)
        img_for_tool_outline_seg = cv2.bitwise_not(img_for_tool_outline_seg)
        # now do the perspective transformation
        img_new, img_for_tool_outline_seg_new = get_perspective_transform(bkgrnd_box, sh_img, img, img_for_tool_outline_seg)
        # clean up the boundary a little bit
        img_for_tool_outline_seg = clean_up_boundary(img_for_tool_outline_seg_new) 
                
        # add/take the OR of the two images -- edge image and segmented image
        img_for_tool_outline = cv2.bitwise_or(img_for_tool_outline_edge, img_for_tool_outline_seg)
        
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            plt.figure();plt.imshow(img_for_tool_outline_seg, cmap='gray')
            plt.figure();plt.imshow(img_for_tool_outline, cmap='gray')
    else:
        img_for_tool_outline = img_for_tool_outline_edge
        
    # check if there are too many edges; if yes then perhaps the background paper has patterns?
    num_non_zero_pixels = cv2.countNonZero(img_for_tool_outline)
    if num_non_zero_pixels >= 0.3*(sh_img[0]*sh_img[1]):
        # too many non-zero pixels in edge image can imply background paper with patterns
        # check if "Background paper out of image" has been signalled or not; if yes, then large part of floor might be exposed
        # and we can incorrectly signal that the paper has patterns
        if ERROR_MESSAGE.find("Background paper has patterns") == -1 and ERROR_MESSAGE.find("Background paper out of image") == -1:
        #if ERROR_MESSAGE.find("Background paper has patterns") == -1:
            ERROR_MESSAGE += "Background paper has patterns. "
            ERROR_THREAT = "red"
            
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':        
        print('number of NON-ZERO PIXELS, ratio to full image = ', num_non_zero_pixels, num_non_zero_pixels/(sh_img[0]*sh_img[1]))
        
    # get the contours around the tools/objects    
    if USE_DOWNSAMPLED_IMAGE == True:
        if len(pruned_cntrs) > 0:
            temp_cntr = pruned_cntrs[0].copy()
        else:
            temp_cntr = []
        pruned_cntrs = []
    else:        
        temp_cntr = get_pruned_cntrs(img_for_tool_outline,img_new)   
        pruned_cntrs = []
        
    num_of_tool = 2
    while (len(temp_cntr) > 0):        
        #print('temp_cntr size:= ', len(np.squeeze(temp_cntr)), len(temp_cntr))
        pruned_cntrs.append(temp_cntr)        
        # now take the convex hull of this contour
        hull = cv2.convexHull(temp_cntr)
        # have to decide whether we want to fill the convex hull with black or the contour itself with black
        ## fill in this convex hull black to effectively remove this contour from the img_for_tool_outline
        #cv2.drawContours(img_for_tool_outline, [hull], -1, (0,0,0), cv2.FILLED)
        # fill in this contour black to effectively remove this contour from the img_for_tool_outline
        cv2.drawContours(img_for_tool_outline, [temp_cntr], -1, (0,0,0), cv2.FILLED)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            figtitle = "image for tool " + str(num_of_tool) + " detection"
            plt.figure(figtitle);plt.imshow(img_for_tool_outline, cmap='gray')
        temp_cntr = []
        temp_cntr = get_pruned_cntrs(img_for_tool_outline,img_new)
        num_of_tool += 1
            
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pruned_cntrs:= ', len(np.squeeze(pruned_cntrs)))        

    ##############################################################################################
    ################### Prepare things to be sent to the tool editor in javascript ###############
    ##############################################################################################     
    if MYENVIRONMENT == 'PYTHON':             
        myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs = data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, True)             
        # check if error message contains "Photo too blurry.." AND "Background paper properly..." AND "Tool too close..."          
        if ERROR_MESSAGE.find("Photo too blurry or tool has no contrast with paper") != -1 and RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR < 1:            
            # just send back the downsample original image in this case -- we failed to find any thing            
            transformed_img_with_cntrs_hulls = np.copy(img)
            # rewrite the image to disk because we have already written it
            cv2.imwrite(os.path.join(folder_jpg_results,fname),transformed_img_with_cntrs_hulls)
        return transformed_img, myrawdata, myhulls, myoffsetX, myoffsetY, bkgrnd_box, sh_img, myprunedCntrs    
    else:            
        myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs = data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, True)        
        # we assume that the paper size is A4. The contours/outlines will be scaled to 297x210 (A4 size)
        # the image sent back to Javascript is not scaled to 297x210, but is scaled within the Javascript
        paper_size = "a4"        
        # we also send the dimensions of the original image in mm
        print("im (width,height)", im.shape[1], im.shape[0])
        width_in_mm = 210
        height_in_mm = 297
        # check if error message contains "Photo too blurry.." AND "Background paper properly..." AND "Tool too close..."          
        if ERROR_MESSAGE.find("Photo too blurry or tool has no contrast with paper") != -1 and RATIO_AREA_MAX_CNTR_AREA_SECOND_MAX_CNTR < 1:
            # just send back the downsample original image in this case -- we failed to find any thing            
            transformed_img_with_cntrs_hulls = np.copy(img)
        # return both the image (background paper with tools) and image with outlines & convexhulls
        return transformed_img, myrawdata, myhulls, myoffsetX, myoffsetY, paper_size, width_in_mm, height_in_mm, transformed_img_with_cntrs_hulls, ERROR_MESSAGE
    

# process image grabbed from the web as it is
def proc_img_from_web(im):
    
    ##############################################################################################
    ###################  Get the image and convert to appropriate size ###########################
    ##############################################################################################
        
    print("shape:= ", im.shape[0], im.shape[1])
    #a_new = im.shape[0]    
    #img = cv2.resize(im,(int(a_new*im.shape[1]/im.shape[0]), a_new))    
    img = im
    
    #img = cv2.resize(im,(a_new,int(a_new*b/a)))    
    sh_img = img.shape
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('original size:',im.shape[0],im.shape[1])
        print('size after resizing to 640 rows: ', sh_img[0], sh_img[1])
    
    # get the grayscale image
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
      
    ##############################################################################################
    ################### Get Tool Outline #########################################################
    ##############################################################################################
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('############### Tool outline detection ####################')
    # a little Gaussian smoothing to remove spurious edges
    blur = cv2.GaussianBlur(gray,(3,3),0) 
    # compute the median of the single channel pixel intensities
    v = np.median(blur)
    # apply automatic Canny edge detection using the computed median
    # use larger SIGMA to get more edges
    lower = int(max(0, (1.0 - 0.75) * v))
    upper = int(min(255, (1.0 + 0.75) * v))
    #lower = 100
    #upper = 200
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('lower %d, upper %d for tool edge detection on transformed image' % (lower, upper))
    edges = cv2.Canny(gray,lower,upper)    
    # "closing" transformation        
    # kernel for "closing" transformation 
    kernel_close = np.ones((7,7),np.uint8)
    img_for_tool_outline_edge = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)       
    
    transformed_img = np.copy(img)
    transformed_img_with_cntrs_hulls = np.copy(img)
        
    # clean up the boundary a little bit
    img_for_tool_outline = clean_up_boundary(img_for_tool_outline_edge)    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure("image for tool 1 detection, perspective -> edge detection");plt.imshow(img_for_tool_outline_edge, cmap='gray')       
        
    # get the contours around the tools/objects    
    pruned_cntrs = []
    temp_cntr = get_pruned_cntrs(img_for_tool_outline,img)
    
    num_of_tool = 2
    while (len(temp_cntr) > 0):
        pruned_cntrs.append(temp_cntr)
        # now take the convex hull of this contour
        hull = cv2.convexHull(temp_cntr)
        # have to decide whether we want to fill the convex hull with black or the contour itself with black
        ## fill in this convex hull black to effectively remove this contour from the img_for_tool_outline
        #cv2.drawContours(img_for_tool_outline, [hull], -1, (0,0,0), cv2.FILLED)
        # fill in this contour black to effectively remove this contour from the img_for_tool_outline
        cv2.drawContours(img_for_tool_outline, [temp_cntr], -1, (0,0,0), cv2.FILLED)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            figtitle = "image for tool " + str(num_of_tool) + " detection"
            plt.figure(figtitle);plt.imshow(img_for_tool_outline, cmap='gray')
        temp_cntr = []
        temp_cntr = get_pruned_cntrs(img_for_tool_outline,img)
        num_of_tool += 1
            
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pruned_cntrs %d' % (len(np.squeeze(pruned_cntrs))))        
        
    ##############################################################################################
    ################### Prepare things to be sent to the tool editor in javascript ###############
    ##############################################################################################    
    
    if MYENVIRONMENT == 'PYTHON':     
        myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs = data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, False);
        return transformed_img, myrawdata, myhulls, myoffsetX, myoffsetY, bkgrnd_box, sh_img, myprunedCntrs    
    else:            
        myrawdata, myhulls, myoffsetX, myoffsetY, myprunedCntrs = data_return_to_js(pruned_cntrs, transformed_img_with_cntrs_hulls, sh_img, False);
        # we assume that the paper size is A4. The contours/outlines will be scaled to 297x210 (A4 size)
        # the image sent back to Javascript is not scaled to 297x210, but is scaled within the Javascript        
        print("im (width,height)", im.shape[1], im.shape[0])
        paper_size = "special"        
        # we also send the dimensions of the original image in mm
        width_in_mm = im.shape[1]/10
        height_in_mm = im.shape[0]/10
        return transformed_img, myrawdata, myhulls, myoffsetX, myoffsetY, paper_size, width_in_mm, height_in_mm, transformed_img_with_cntrs_hulls, ERROR_MESSAGE
        

if MYENVIRONMENT == 'PYTHON':
    print('Inside the __main__ function!')
    if __name__ == '__main__':        
        print('python version:= ', sys.version)
        # folders for all the input data and output results    
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//All Tools'
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools new jpeg'
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools jpeg'
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Library//ALL Home Depot JPG'
        #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools trouble jpeg' 
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_input'
        folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_input_latest'
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_Error_Codes'        
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_Error_Codes_Bad'
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_Light_Background'        
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_Light_Background_new'
        #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_paper_with_patterns'                
        #folder_jpg_results = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Debugging//Test_output'      
        folder_jpg_results = 'D://home//work//DigitalHome//DigitalToolBox//Test_output_1'      
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            fname = 'UTC3E9PP1HJB.jpg'
            print(fname) 
            im = cv2.imread(os.path.join(folder_jpg,fname))
            fov = 0
            zoom_factor = 1.0
            cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, bkgrnd_box, sh_img, myprunedCntrs = get_contours(im, fov, zoom_factor) 
            print("ERROR_MESSAGE:= ", ERROR_MESSAGE)
            print("ERROR_THREAT:= ", ERROR_THREAT)
        else:
            folder_ErrorMessage_results = 'D://home//work//DigitalHome//DigitalToolBox'            
            fileErrMsg = open(os.path.join(folder_ErrorMessage_results,"errorMessages_2.txt"),'w')
            for fname in os.listdir(folder_jpg):                                        
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"): 
                    print(fname) 
                    im = cv2.imread(os.path.join(folder_jpg,fname))
                    fov = 0
                    zoom_factor = 1.0
                    cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, bkgrnd_box, sh_img, myprunedCntrs = get_contours(im, fov, zoom_factor)                   
                    print("ERROR_MESSAGE:= ", ERROR_MESSAGE)
                    if ERROR_MESSAGE != "":                        
                        fileErrMsg.write('%s %s\n\n' % (fname,ERROR_MESSAGE))
                    ERROR_MESSAGE = ""
                    ERROR_THREAT = ""
                    #cv2.imwrite(os.path.join(folder_jpg_results,fname),cvImagewithCntrs)                
                    # write to fname.txt when debug mode is on
                    if TESTING == True and MYENVIRONMENT == 'PYTHON': 
                        #folder_txt_results = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools test txt'
                        folder_txt_results = 'D://home//work//DigitalHome//DigitalToolBox//Test_txt_output_2'
                        filename = os.path.splitext(fname)[0]
                        file = open(os.path.join(folder_txt_results,filename+".txt"),'w')
                        # write the size of the original image
                        file.write('%d %d # orig image (rows,cols) # \n' % (sh_img[0],sh_img[1]))
                        # write the background box coordinates              
                        if len(bkgrnd_box) > 0:
                            for i in range(0,len(bkgrnd_box[0])):
                                file.write('%d %d ' % (bkgrnd_box[0][i,0],bkgrnd_box[0][i,1]))
                            file.write('# background paper coordinates # \n')
                        else:
                            file.write('-1 # background paper coordinates # \n')
                        # write the number of tools 
                        file.write('%d # number of tools # \n' % (len(myprunedCntrs)))
                        # write the offsetx and offsety                        
                        for i in range(0,len(myprunedCntrs)):
                            #print("i:= ", i)
                            file.write('%.2f %.2f # offsetX and offsetY # \n' % (myoffsetX[i], myoffsetY[i]))
                        # write the convex hulls
                        i = 0
                        for c in myprunedCntrs:
                            # get the convex hull
                            hull = cv2.convexHull(c) 
                            hull = np.squeeze(hull)
                            file.write('%d ' % (len(hull)))
                            file.write('# number of points in convexhull no. %d # \n' % (i+1))
                            for j in range(0,len(hull)):
                                file.write('%.2f %.2f ' % (hull[j][0], hull[j][1]))
                            file.write('# convexhull no. %d # \n' % (i+1))
                            i = i+1
                        # write the tool path
                        i = 0
                        for c in myprunedCntrs:  
                            cntr = np.squeeze(c)
                            file.write('%d %.2f %.2f ' % (len(cntr), cntrperi(cntr), cntrarea(cntr)))
                            file.write('# number of points, perimeter, area of contour no. %d # \n' % (i+1))                                  
                            for j in range(0,len(cntr)):
                                file.write('%.2f %.2f ' % (cntr[j][0], cntr[j][1]))
                            file.write('# contour no. %d # \n' % (i+1))
                            i = i+1
                        file.close()            
                else:
                    print("Error: Need input files to be in jpeg format")
            fileErrMsg.close()

