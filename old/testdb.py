# -*- coding: utf-8 -*-
"""
Created on Sat Apr 18 13:18:09 2020

@author: 
"""

# README
# install postgres (databse server)
# start the database server with command: postgres -D "C:/Program Files/PostgreSQL/12/data"
# for other users find out where the "data" folder is stored in your machine and use that above after -D
# After the database has started we can create a databsase with the command: createdb -U postgres mydb 
# password will be asked and the password is set during installation
# to see the database on command prompt with command: psql -U postgres mydb
# create a table with command:  CREATE TABLE myUserData (filename text, browser_type text, timestamp text, size_of_file integer);
# start pgadmin application to view the database in the browser
# you can see the database server, the database connected to it and its entries
# to see the entries go to mydb -> schemas -> Tables -> myuserdata -> right-click -> view/edit data -> all rows

import psycopg2

# connect to the database
con = psycopg2.connect(host="localhost", database="mydb", user="postgres", password="Kudekar", port=5432)

# cursor
cur = con.cursor()

# insert into database
cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", ("hammer", "chrome", "2020_04_18_01_28", 50))

# execute query
cur.execute("select filename, browser_type, timestamp, size_of_file from myuserdata")

rows = cur.fetchall()

for r in rows:
    print (f"filename {r[0]} browser_type {r[1]} timestamp {r[2]} size_of_file {r[3]}")
    

# commit the transaction otherwise you will not see it in pgadmin
con.commit()

# close the cursor
cur.close()


# close the connection
con.close()




