# -*- coding: utf-8 -*-
"""
Created on Fri Nov  8 09:09:21 2019

@author: 
"""


import numpy as np
import math
import cv2
#import imutils
#import os
import matplotlib.pyplot as plt
#import skimage.segmentation as seg
#from skimage.filters import gaussian
#from skimage.segmentation import chan_vese
#from scipy.interpolate import splprep, splev

THRESH_CONTOUR = 50 # was set to 250 originally
TOLERANCE_DIST = 40
THRESH_AREA_MIN = 0.001 # was set to 0.003 originally
THRESH_SLOPE = 1e-10
SLOPE_MAX = 1e10
THRESH_BKGRND_MIN = 0.55 # this needs to change to 0.35 for <PERSON><PERSON>'s picture
THRESH_AREA_MAX = THRESH_BKGRND_MIN # although we need to detect the background box, we don't need it as a starting contour for object detection
THRESH_BKGRND_MAX = 0.85
TOLERANCE_BKGRND = 10

USE_SEGMENTATION = False

# variance of histogram
def var_hist(h):
    avg = 0
    var = 0
    for i in range(0,len(h)):
        avg += i*h[i]/np.sum(h)
    for i in range(0,len(h)):
        var += i*i*h[i]/np.sum(h)
    var = var - avg*avg
    return var

# function to determine the perimeter of contours
def cntrperi(cnt):
   return cv2.arcLength(cnt,True)

# get angle of line between two points a, b
def get_angle(a,b):
    if np.abs(a[0]-b[0]) <= THRESH_SLOPE:
        m = np.pi/2
    else:
        m = math.atan((a[1]-b[1])/(a[0]-b[0]))
    
    return m

# get the area of a rectangular box
def get_area(box):
    h = np.linalg.norm(box[1]-box[2])
    w = np.linalg.norm(box[2]-box[3])
    
    return h*w

# given box1 and box2, check if one is inside the other
def check_box_within_box(box1,box2):
    # output: 1 if box2 is within box1; 2 if box1 is within box2; 0 else
    area1 = get_area(box1); area2 = get_area(box2)
    h1 = np.linalg.norm(box1[1]-box1[2])
    w1 = np.linalg.norm(box1[2]-box1[3])
    h2 = np.linalg.norm(box2[1]-box2[2])
    w2 = np.linalg.norm(box2[2]-box2[3])
    flag = 0
    if area1 >= area2:
        l1 = box1[0] - box1[1];  l2 = box1[1] - box1[2]; l3 = box1[2] - box1[3]; l4 = box1[3] - box1[0];
        for j in range(0,len(box2)):
            d1 = np.cross(l1,box1[0]-box2[j])/np.linalg.norm(l1);
            d2 = np.cross(l3,box1[2]-box2[j])/np.linalg.norm(l3);
            d3 = np.cross(l2,box1[1]-box2[j])/np.linalg.norm(l2);
            d4 = np.cross(l4,box1[3]-box2[j])/np.linalg.norm(l4);
            da = np.abs(d1)+np.abs(d2); db = np.abs(d3)+np.abs(d4)
            if (np.abs(da - h1) <= TOLERANCE_DIST) and (np.abs(db - w1) <= TOLERANCE_DIST):
                flag = 1
            else:
                flag = 0
                break
    else:
        l1 = box2[0] - box2[1];  l2 = box2[1] - box2[2]; l3 = box2[2] - box2[3]; l4 = box2[3] - box2[0];
        for j in range(0,len(box1)):
            d1 = np.cross(l1,box2[0]-box1[j])/np.linalg.norm(l1);
            d2 = np.cross(l3,box2[2]-box1[j])/np.linalg.norm(l3);
            d3 = np.cross(l2,box2[1]-box1[j])/np.linalg.norm(l2);
            d4 = np.cross(l4,box2[3]-box1[j])/np.linalg.norm(l4);
            da = np.abs(d1)+np.abs(d2); db = np.abs(d3)+np.abs(d4)
            if (np.abs(da - h2) <= TOLERANCE_DIST) and (np.abs(db - w2) <= TOLERANCE_DIST):
                flag = 2   
            else:
                flag = 0
                break
        
    return flag

# remove boxes within a box
def remove_inner_boxes(rect_box, good_box, all_hulls, candidate_cntrs, img_copy):
    pruned_box = [] # remove boxes which are within a box and prune the original list of boxes
    pruned_hulls = []
    pruned_cntrs = []
    # consider every pair of boxes and check if one box is within the other
    for i in range(0,len(rect_box)):
        if good_box[i] == True:
            for j in range(0,len(rect_box)):
                if i != j:
                    flag = check_box_within_box(rect_box[i],rect_box[j])
                    if flag == 1: # j is within i
                        good_box[j] = False
                    if flag == 2: # i is within j
                        good_box[i] = False
                        break       
    for i in range(0,len(rect_box)):
        if good_box[i] == True:                
            pruned_box.append(rect_box[i])
            pruned_hulls.append(all_hulls[i])
            pruned_cntrs.append(candidate_cntrs[i])
            cv2.drawContours(img_copy,[rect_box[i]],0,(0,255,0),4)
            cv2.drawContours(img_copy, [all_hulls[i]], -1, (0,0,255), 4) 
    return pruned_box, pruned_hulls, pruned_cntrs

# finds the intersection of two lines given in Hesse normal form. Returns closest integer pixel locations.
# See https://stackoverflow.com/a/383527/5087436
def intersection(line1, line2):

    rho1 = line1[0]; theta1 = line1[1]
    rho2 = line2[0]; theta2 = line2[1]
    A = np.array([
        [np.cos(theta1), np.sin(theta1)],
        [np.cos(theta2), np.sin(theta2)]
    ])
    b = np.array([[rho1], [rho2]])
    x0, y0 = np.linalg.solve(A, b)
    x0, y0 = int(np.round(x0)), int(np.round(y0))
    return np.array([x0, y0],dtype=np.int32)

# "grab" the rectangle which encapsulates the background paper
def get_bkgrnd_box(img_closing, sh_img):            
    # get Hough lines to get the four boundary lines around the background paper
    if USE_SEGMENTATION:
        inv_closing = cv2.bitwise_not(img_closing)
        edges_closing = cv2.Canny(inv_closing,100,200)
    else:
        edges_closing = cv2.Canny(img_closing,100,200)
        
    lines = cv2.HoughLines(edges_closing,1,np.pi/180,threshold = 125)
    lines = np.squeeze(lines)
    # get only those lines which are either close to 0 deg slope or 90 deg slope
    pruned_lines = -10000*np.ones((lines.shape[0],lines.shape[1]),dtype=np.float32)
    k = 0
    for i in range(0,len(lines)):   
        rho = lines[i][0]
        theta = lines[i][1]
        a = np.cos(theta)
        b = np.sin(theta)
        if (np.abs(a) < 0.15 or np.abs(a) > 0.85): 
            # need to take care when rho is negative
            if rho > 0:
                pruned_lines[k,0] = rho; pruned_lines[k,1] = theta
            else:
                pruned_lines[k,0] = -1.0*rho; pruned_lines[k,1] = np.pi - theta
            k = k+1

    pruned_lines = pruned_lines[pruned_lines[:,1] > -10000]
    pruned_lines = np.float32(pruned_lines)
    # cluster to get 4 strong lines 
    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 50, 0.1) 
    centers = []
    if len(pruned_lines) > 0: # hope to get 4 cluster centers to denote the 4 strong lines of the box surrounding backgrnd paper
        ret,label,centers = cv2.kmeans(np.squeeze(pruned_lines),4,None,criteria,10,cv2.KMEANS_RANDOM_CENTERS)
        temp_bkgrnd_box = []
    if len(centers) == 4:
        for i in range(0,len(centers)-1):         
            for j in range(i+1,len(centers)):
                line1 = centers[i]; line2 = centers[j]
                if np.abs(line1[1] - line2[1]) > 1e-2: # make sure they aren't parallel lines
                    a = intersection(line1, line2)
                    if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):
                        temp_bkgrnd_box.append(a)        
        
    # get bkgrnd via contours
    # get contours around the segmented objects
    major = cv2.__version__.split('.')[0]
    if major == '3':
        print("wrong")
        im2, cnts, hier = cv2.findContours(img_closing, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    else:
        cnts, hier = cv2.findContours(img_closing, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrperi, reverse = True)[:100]
    
    bkgrnd_box = []
    for c in cnts:
    	# if the contour has small perimeter, don't draw it
        if cntrperi(c) > THRESH_CONTOUR:
            rect = cv2.minAreaRect(c)
            #print(rect)
            box = cv2.boxPoints(rect)
            box = np.int0(box)              
            # get the rectangle which is between 0.6 and 0.8 of the area of the image; that should mostly be the background paper
            if (get_area(box)/(sh_img[0]*sh_img[1]) >= THRESH_BKGRND_MIN) and (get_area(box)/(sh_img[0]*sh_img[1]) <= THRESH_BKGRND_MAX):          
                #print(get_area(box)/(sh_img[0]*sh_img[1]))            
                bkgrnd_box.append(box)
                #cv2.drawContours(gray_copy,[c],0,(255,255,255),4)  
                #cv2.drawContours(gray_copy,[box],0,(0,255,0),4)  

    # what if we find more than 1 bkgrnd_box; can happen because of noise/shadows
    # take the box with minimum area (this could be refined)
    bkgrnd_box_temp = []
    min_area = 1.0
    if len(bkgrnd_box) > 1:
        for i in range(0,len(bkgrnd_box)):
            area = get_area(bkgrnd_box[i])/(sh_img[0]*sh_img[1])
            if  area <= min_area:
                min_area = area
                min_ind = i
        bkgrnd_box_temp.append(bkgrnd_box[min_ind])
    else: 
        bkgrnd_box_temp = bkgrnd_box

    bkgrnd_box = bkgrnd_box_temp
    # refine the bkgrnd_box with the temp_bkgrnd_box obtained previously
    if len(temp_bkgrnd_box) == 4:
        for i in range(0,len(bkgrnd_box[0])):
            for j in range(0,len(temp_bkgrnd_box)):
                a = bkgrnd_box[0][i]; b = temp_bkgrnd_box[j]
                if np.abs(a[0]-b[0]) <= 25 and np.abs(a[1]-b[1]) <= 25:
                    if a[0] <= sh_img[1]/2:
                        bkgrnd_box[0][i,0] = np.maximum(a[0],b[0])
                    else: 
                        bkgrnd_box[0][i,0] = np.minimum(a[0],b[0])
                    if a[1] <= sh_img[0]/2:
                        bkgrnd_box[0][i,1] = np.maximum(a[1],b[1])
                    else:
                        bkgrnd_box[0][i,1] = np.minimum(a[1],b[1])                
    
    return bkgrnd_box

# perscpective transformation 
def get_perspective_transform(bkgrnd_box, sh_src, sh_dst, img, gray, img_closing):                  
    #pts2 = np.float32([[0,0],[sh_img[1],0],[sh_img[1],sh_img[0]],[0,sh_img[0]]])
    #pts2 = np.float32([[0,0],[int(scale*A4_w),0],[int(scale*A4_w),int(scale*A4_h)],[0,int(scale*A4_h)]])
    
    pts2 = np.float32([[0,0],[sh_src[1],0],[sh_src[1],sh_src[0]],[0,sh_src[0]]])
    pts1 = -1*np.ones(np.shape(pts2))
    for i in range(0,4):
        d = 100000
        for j in range(0,len(bkgrnd_box[0])):        
            if np.linalg.norm(pts2[i]-bkgrnd_box[0][j]) <= d:
                d = np.linalg.norm(pts2[i]-bkgrnd_box[0][j])
                pts1[i,:] = bkgrnd_box[0][j]
                
    pts1 = np.float32(pts1)
    pts2 = np.float32([[0,0],[sh_dst[1],0],[sh_dst[1],sh_dst[0]],[0,sh_dst[0]]])
    # rect height and width
    #width = np.linalg.norm(pts1[0]-pts1[1])
    #height = np.linalg.norm(pts1[1]-pts1[2])
    M = cv2.getPerspectiveTransform(pts1,pts2)
    #gray_new = cv2.warpPerspective(gray,M,(sh_img[1],sh_img[0]))
    #img_new = cv2.warpPerspective(img,M,(sh_img[1],sh_img[0]))
    #gray_new = cv2.warpPerspective(gray,M,(int(scale*A4_w),int(scale*A4_h)))
    #img_new = cv2.warpPerspective(img,M,(int(scale*A4_w),int(scale*A4_h)))
    gray_new = cv2.warpPerspective(gray,M,(sh_dst[1],sh_dst[0]))
    img_new = cv2.warpPerspective(img,M,(sh_dst[1],sh_dst[0]))
    closing_new = cv2.warpPerspective(img_closing,M,(sh_dst[1],sh_dst[0]))
    
    return img_new, gray_new, closing_new

# smooth the contour by B-splines
def get_smoothened_cntr(contour):
    smoothened = []
    x,y = contour.T
    # Convert from numpy arrays to normal arrays
    x = x.tolist()[0]
    y = y.tolist()[0]
    # https://docs.scipy.org/doc/scipy-0.14.0/reference/generated/scipy.interpolate.splprep.html
    tck, u = splprep([x,y], u=None, s=560.0, per=1)
    # https://docs.scipy.org/doc/numpy-1.10.1/reference/generated/numpy.linspace.html
    u_new = np.linspace(u.min(), u.max(), 70)
    # https://docs.scipy.org/doc/scipy-0.14.0/reference/generated/scipy.interpolate.splev.html
    x_new, y_new = splev(u_new, tck, der=0)
    # Convert it back to numpy format for opencv to be able to display it
    res_array = [[[int(i[0]), int(i[1])]] for i in zip(x_new,y_new)]
    smoothened.append(np.asarray(res_array, dtype=np.int32))
    
    return smoothened
    

# find_contours followed by pruning the contours
def get_pruned_cntrs(img_closing,img_gray,img_orig):
    sh = img_orig.shape
    ## simple binary thresholding as a segmentation technique; could use fancier segmentation
    #ret,th = cv2.threshold(img_gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
    #img_seg = cv2.bitwise_not(th)
    #kernel_close = np.ones((11,11),np.uint8)
    ## remove noise
    #closing = cv2.morphologyEx(img_seg, cv2.MORPH_CLOSE, kernel_close)
    
    # get contours around the segmented objects
    #im2, cnts, hier = cv2.findContours(closing, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    major = cv2.__version__.split('.')[0]
    if major == '3':
        print("wrong")
        im2, cnts, hier = cv2.findContours(img_closing, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    else:
        cnts, hier = cv2.findContours(img_closing, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    
    cnts = sorted(cnts, key = cntrperi, reverse = True)[:100] #sort the top 20
    
    rect_box = [] # min rect around a contour
    good_box = [] # good_box = true implies a box is not consumed by any another box 
    all_hulls = [] # list of convex hull around contour
    candidate_cntrs = [] # contours which are neither too big nor too small
    for c in cnts:
    	# if the contour is small perimeter, don't draw it
        if cntrperi(c) > THRESH_CONTOUR:
            #epsilon = 0.1*cv2.arcLength(c,True)
            #approx = cv2.approxPolyDP(c,epsilon,True)              
            cv2.drawContours(img_orig, [c], -1, (0,255,0), 4)  
            rect = cv2.minAreaRect(c)
            #print(rect)
            box = cv2.boxPoints(rect)
            box = np.int0(box)      
            #print(get_area(box)/(sh[0]*sh[1]))
            # remove very large rectangles (the background paper) and very small rectangles (just noise)
            if (get_area(box)/(sh[0]*sh[1]) >= THRESH_AREA_MIN) and (get_area(box)/(sh[0]*sh[1]) <= THRESH_AREA_MAX):          
                rect_box.append(box)
                good_box.append(True) # initially assume that all boxes are good. they will be refined later
                candidate_cntrs.append(c)
                #cv2.drawContours(im_bl, [c], -1, (255,255,255), 3) 
                #print(box)
                #print(c)
                hull = cv2.convexHull(c)
                all_hulls.append(hull)
                cv2.drawContours(img_orig, [hull], -1, (0,0,255), 4) 
    # prune things, i.e., remove contours-within-contours/boxes-within-boxes  
    img_copy = np.copy(img_orig)
    pruned_box, pruned_hulls, pruned_cntrs = remove_inner_boxes(rect_box, good_box, all_hulls, candidate_cntrs, img_copy)
    
    return pruned_box, pruned_cntrs, pruned_hulls

# get the slope and intercept of the line from point a to point b
def get_line(a,b):
    m = 0; c = a[1]
    if np.abs(b[0] - a[0]) < THRESH_SLOPE:
        m = SLOPE_MAX # vertical line
    else:
        m = (b[1] - a[1])/(b[0] - a[0])
        c = (a[1]*b[0] - b[1]*a[0])/(b[0] - a[0])
                
    return m,c
    
# hull is just a bunch of coordinates and we want to put equally spaced points to create a dense contour
def hull_points(resolution, hull):
    # get length of the hull 
    len_hull = 0
    for i in range(0,hull.shape[0]):
        if i < hull.shape[0]-1:
            len_hull += np.linalg.norm(hull[i]-hull[i+1])
        else:
            len_hull += np.linalg.norm(hull[i]-hull[0])
    
    spacing = len_hull/resolution
    
    x = []; y = []
    for i in range(0,hull.shape[0]):
        if i < hull.shape[0]-1:
            a = hull[i]; b = hull[i+1]
        else:
            a = hull[i]; b = hull[0]
        m,c = get_line(a,b)
        dist = np.linalg.norm(a-b)
        temp_x =  np.linspace(a[0],b[0], int(np.ceil(dist/spacing)))
        if m >= SLOPE_MAX:
            temp_y = np.linspace(a[1],b[1], int(np.ceil(dist/spacing)))
        else:
            temp_y = m*temp_x + c
        if i == 0:
            x = np.copy(temp_x)
            y = np.copy(temp_y)
        else:
            x = np.append(x,temp_x)
            y = np.append(y,temp_y)
      
    return np.array([x, y]).T         

def image_show(image, nrows=1, ncols=1, cmap='gray'):
    fig, ax = plt.subplots(nrows=nrows, ncols=ncols, figsize=(14, 14))
    ax.imshow(image, cmap='gray')
    ax.axis('off')
    return fig, ax


# use the convexity defect function to improve the convex hull
THRESH_CONV = 50
def get_better_hull(cntr,hull):
    # get the convexity defect points
    temp_hull = cv2.convexHull(cntr,returnPoints = False)
    defects = cv2.convexityDefects(cntr,temp_hull)
    
    for i in range(defects.shape[0]):
        s,e,f,d = defects[i,0]
        lstart = cntr[s][0]
        lend = cntr[e][0]
        far = cntr[f][0]
        dist = np.linalg.norm(np.cross(lend - lstart, far - lstart)/np.linalg.norm(lend - lstart))
        if dist >= THRESH_CONV: # include the defect point in the hull
            #print(i,dist,lstart,lend,far)
            ind = np.where(((hull[:,0,0]==lend[0]) & (hull[:,0,1]==lend[1])))
            ind = int(ind[0]) # convert tuple to int            
            a = lstart + 0.2*(lend - lstart)
            b = lend + 0.2*(lstart - lend)
            hull = np.concatenate((hull[0:ind+1,:,:], b.reshape((1,1,2)), far.reshape((1,1,2)), a.reshape((1,1,2)), hull[ind+1:len(hull),:,:]))
            
    return hull
    
# shrink the convex hull by pulling it towards the original contour
def shrink_hull(hull_cntr,cntr):
   orig_cntr = np.squeeze(cntr)   
   closest_cntr = np.copy(hull_cntr)
   #hull_cntr = hull_points(len(orig_cntr), np.squeeze(hull))[:-1] 
   # find the point on orig_cntr closest to a given point on the hull_cntr
   max_dist = 0
   for i in range(0,len(hull_cntr)):
       a = orig_cntr - hull_cntr[i]
       b = np.sqrt(a[:,0]*a[:,0]+a[:,1]*a[:,1])
       ind = np.argmin(b)       
       closest_cntr[i,:] = orig_cntr[ind,:]
       if b[ind] >= max_dist:
           max_dist = b[ind]
   
   # pull the hull_cntr towards the closest_pnt       
   #t = 0.765
   for i in range(0,len(hull_cntr)):
       t = 0.8*np.linalg.norm(hull_cntr[i,:]-closest_cntr[i,:])/max_dist       
       if np.linalg.norm(hull_cntr[i,:] - closest_cntr[i,:]) > 10:
           hull_cntr[i,:] = (1-t)*hull_cntr[i,:] + t*closest_cntr[i,:]
   
   return np.int32(hull_cntr)

# run unconstrained active contour methods around the convex hulls. Useful when there are 
# shadows and we can tune the method to snap to the real object
# also write to .dxf file   
def get_snakes(pruned_hulls, pruned_cntrs, img_gray, img_end, img_start):
    sh = img_gray.shape
    fig, ax = image_show(img_end) 
    fig1, ax1 = image_show(img_start) 
    for i in range(0,len(pruned_hulls)):
    #for i in range(0,len(pruned_box)):
        #rect_corners = pruned_box[i]
        #points = rect_points(400, rect_corners)#[:-1]
        
        #hull = np.squeeze(pruned_hulls[i])
        #points = hull_points(1600, hull)[:-1]
        #points = shrink_hull(points,pruned_cntrs[i])
        
        hull_new = get_better_hull(pruned_cntrs[i],pruned_hulls[i])
        points = hull_points(1600, np.squeeze(hull_new))[:-1]
        points = shrink_hull(points,pruned_cntrs[i])
        # if the points are hugging the image boudary then pull them inside
        points[points[:,0] == 0] = points[points[:,0] == 0] + 7
        points[points[:,0] == sh[1]] = points[points[:,0] == sh[1]] - 7
        points[points[:,1] == 0] = points[points[:,1] == 0] + 7
        points[points[:,1] == sh[0]] = points[points[:,1] == sh[0]] - 7
        ax1.plot(points[:, 0], points[:, 1], '-b', lw=3) 
        # Use Active Contour image segmentation method to get the boundary of the object
        # the starting contour is the rectangular box obtained from above segmentation and contouring
        snake = seg.active_contour(img_gray, points, alpha=0.01, beta=0.1, w_edge=0.3, w_line=-0.05)
        # check if contour is absurd
        # 1st check: xmin,xmax,ymin,ymax are within the image
        xmin = np.min(snake[:,0]); xmax = np.max(snake[:,0])
        ymin = np.min(snake[:,1]); ymax = np.max(snake[:,1])
        
        if (xmin >= 0 and xmin <= sh[1] and xmax >= 0 and xmax <= sh[1] and ymin >= 0 and ymin <= sh[0] and ymax >= 0 and ymax <= sh[0]):
            ax.plot(snake[:, 0], snake[:, 1], '-b', lw=3)  
            # write to .dxf file
            points = np.squeeze(snake)
            pts = list(map(tuple,points))
            lwp = msp.add_lwpolyline(pts)
            lwp.closed = True


# for images where object has a little part beyond the background paper we cleanup the boundary
def clean_up_boundary(im):
    # just make the pixels at the boundary black
    THRESH_BNDRY = 9 # this changes to 9 for IMG_5015
    im[0:THRESH_BNDRY, :] = 0
    im[im.shape[0]-THRESH_BNDRY:im.shape[0], :] = 0
    im[im.shape[0]-THRESH_BNDRY:im.shape[0], :] = 0
    im[:,0:THRESH_BNDRY] = 0
    im[:,im.shape[1]-THRESH_BNDRY:im.shape[1]] = 0
    
    return im


# given an initial contour or set of points, get a final contour
def get_cntr_given_init_cntr(init_cntr, img_gray, img_start, img_end):
    sh = img_gray.shape
    fig, ax = image_show(img_end) 
    fig1, ax1 = image_show(img_start)
    #assume that the contour is in a given order; clockwise or anti-clockwise doesn't matter; just an order is enough
    points = hull_points(1600, init_cntr)[:-1]
    # if the points are hugging the image boudary then pull them inside
    points[points[:,0] == 0] = points[points[:,0] == 0] + 7
    points[points[:,0] == sh[1]] = points[points[:,0] == sh[1]] - 7
    points[points[:,1] == 0] = points[points[:,1] == 0] + 7
    points[points[:,1] == sh[0]] = points[points[:,1] == sh[0]] - 7
    ax1.plot(points[:, 0], points[:, 1], '-b', lw=3) 
    # run active contour to snap to the object
    # Use Active Contour image segmentation method to get the boundary of the object
    # the starting contour is the rectangular box obtained from above segmentation and contouring
    snake = seg.active_contour(img_gray, points, alpha=0.01, beta=0.1, w_edge=0.3, w_line=-0.05)
    xmin = np.min(snake[:,0]); xmax = np.max(snake[:,0])
    ymin = np.min(snake[:,1]); ymax = np.max(snake[:,1])
        
    if (xmin >= 0 and xmin <= sh[1] and xmax >= 0 and xmax <= sh[1] and ymin >= 0 and ymin <= sh[0] and ymax >= 0 and ymax <= sh[0]):
        ax.plot(snake[:, 0], snake[:, 1], '-b', lw=3)  
    
    final_cntr = snake
    
    return final_cntr

# check if perpendicular line drawn from given_pt to line(pt1,pt2) lies mostly in the bkgrnd or not
def check_outside_cntr(given_pt, a, b, dist, bkgrnd_color, gray_new):
    flag = True
    
    
    if np.abs(b[0,0] - a[0,0]) <= 1e-7: # vertical line; 
        intersection_pt = np.array([[a[0,0],given_pt[0,1]]],dtype = np.int32)
        xstart = given_pt[0,0]; y = given_pt[0,1]
        num_pts = np.abs(xstart-intersection_pt[0,0])+1
        line_img = np.zeros(num_pts,dtype = "uint8")
        for i in range(0,num_pts):            
            x = xstart + i*np.sign(intersection_pt[0,0] - xstart)
            line_img[i] = gray_new[y,x]
    elif np.abs(b[0,1] - a[0,1]) <= 1e-7: # horizontal line: 
        intersection_pt = np.array([[given_pt[0,0],a[0,1]]],dtype = np.int32)
        x = given_pt[0,0]; ystart = given_pt[0,1]
        num_pts = np.abs(ystart-intersection_pt[0,1])+1
        line_img = np.zeros(num_pts,dtype = "uint8")
        for i in range(0,num_pts):            
            y = np.int32(ystart + i*np.sign(intersection_pt[0,1] - ystart))
            line_img[i] = gray_new[y,x]
    else: # non-trivial line with slope m        
        m = (b[0,1] - a[0,1])/(b[0,0] - a[0,0])
        x = np.int32(m/(1+m*m)*(given_pt[0,1] + given_pt[0,0]/m - a[0,1] + m*a[0,0]))
        y = np.int32(m*x + a[0,1] - m*a[0,0])
        intersection_pt = np.array([[np.minimum(x,gray_new.shape[1]-1),np.minimum(y,gray_new.shape[0]-1)]], dtype = np.int32)  
        xstart = given_pt[0,0]; ystart = given_pt[0,1]
        num_pts = np.abs(xstart-intersection_pt[0,0])+1
        line_img = np.zeros(num_pts,dtype = "uint8")
        for i in range(0,num_pts):            
            x = np.int32(xstart + i*np.sign(intersection_pt[0,0] - xstart))
            y = np.int32(-1.0/m*x + given_pt[0,1] + 1/m*given_pt[0,0])
            #print(x,y)
            line_img[i] = gray_new[np.minimum(y,gray_new.shape[0]-1),np.minimum(x,gray_new.shape[1]-1)]
    
    hist = cv2.calcHist([line_img], [0] , None, [255] , [0, 255])
    
    #if np.abs(np.argmax(hist) - bkgrnd_color) >= 60:
    # large std_dev in the grayscale histogram implies that the perpendicular line isn't going through only bkgrnd
    if np.sqrt(var_hist(hist)) >= 15:
        flag = False
    
    return flag

# given a point, make a given contour/hull pass through it
def make_cntr_pass_thru_given_pt(given_pt, hull, bkgrnd_color, gray_new):
    
    min_dist = 1e7
    for i in range(0,len(hull)):
        if i == len(hull) - 1:
            pt1 = hull[i]; pt2 = hull[0]
        else:
            pt1 = hull[i]; pt2 = hull[i+1]    
        dist = np.abs(np.cross(given_pt-pt1,pt2 - pt1)/np.linalg.norm(pt2-pt1))
        if dist <= min_dist and check_outside_cntr(given_pt, pt1, pt2, dist, bkgrnd_color, gray_new):
            min_dist = dist
            min_ind = i
    
    # drop perpendiclar from given_pt to the line from pt1 to p2. If the perp line intersection lies on the line, then split
    # the hull, else extend the hull in the appropriate direction
    a = hull[min_ind]; b = hull[min_ind+1]
    if np.abs(b[0,0] - a[0,0]) <= 1e-7: # vertical line
        intersection_pt = np.array([[a[0,0],given_pt[0,1]]],dtype = np.int32)
    elif np.abs(b[0,1] - a[0,1]) <= 1e-7: # horizontal line: 
        intersection_pt = np.array([[given_pt[0,0],a[0,1]]],dtype = np.int32)
    else:
        # non-trivial line
        m = (b[0,1] - a[0,1])/(b[0,0] - a[0,0])
        x = np.int32(m/(1+m*m)*(given_pt[0,1] + given_pt[0,0]/m - a[0,1] + m*a[0,0]))
        y = np.int32(m*x + a[0,1] - m*a[0,0])
        intersection_pt = np.array([[x,y]], dtype = np.int32)
        
    if (intersection_pt[0,0] >= np.minimum(a[0,0],b[0,0]) and intersection_pt[0,0] <= np.maximum(a[0,0],b[0,0])) and (intersection_pt[0,1] >= np.minimum(a[0,1],b[0,1]) and intersection_pt[0,1] <= np.maximum(a[0,1],b[0,1])):
        # given_pt in the middle
        hull = np.concatenate((hull[0:min_ind+1,:,:], given_pt.reshape((1,1,2)), hull[min_ind+1:len(hull),:,:]))
    elif (np.linalg.norm(intersection_pt-a) <= np.linalg.norm(intersection_pt-b)): # given_pt before a
        hull = np.concatenate((hull[0:min_ind,:,:], given_pt.reshape((1,1,2)), hull[min_ind,:,:], hull[min_ind+1:len(hull),:,:]))
    else: # given point after b
        hull = np.concatenate((hull[0:min_ind+1,:,:], hull[min_ind+1,:,:],  given_pt.reshape((1,1,2)), hull[min_ind+2:len(hull),:,:]))
    
    return hull


##### main function #########
if __name__ == "__main__":

    # get the grayscale image
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    gray_copy = np.copy(gray)
    # HSV format; could be useful not sure
    img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    img_h, img_s, img_v = img_hsv[:, :, 0], img_hsv[:, :, 1], img_hsv[:, :, 2]
    
    # morphological kernels
    kernel_close = np.ones((3,3),np.uint8)
    kernel_close1 = np.ones((5,5),np.uint8) # kernel size of 9 was used for plain/light background images
    
    if USE_SEGMENTATION:
    # simple thresholding for segmentation        
        ret,th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        img_seg = cv2.bitwise_not(th)
    
        #Chan-Vese segmentation
        #blur = cv2.GaussianBlur(gray_new,(5,5),0)
        #edges = cv2.Canny(blur,100,200)
        #cv = chan_vese(blur, mu=0.85, lambda1=1, lambda2=1, tol=1e-3, max_iter=200,
        #              dt=0.75, init_level_set="checkerboard", extended_output=True)
    
        #img_seg = 255*cv[0]
        #img_seg = np.uint8(img_seg)
        #img_seg = cv2.bitwise_not(img_seg)
        
        # remove noise
        closing = cv2.morphologyEx(img_seg, cv2.MORPH_CLOSE, kernel_close)
        #opening = cv2.morphologyEx(closing, cv2.MORPH_OPEN, kernel_open)
    
    else:
        blur = cv2.GaussianBlur(gray,(3,3),0)
        edges = cv2.Canny(blur,100,200)    
        closing = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)
        closing = cv2.morphologyEx(closing, cv2.MORPH_CLOSE, kernel_close1)
        #opening = cv2.morphologyEx(closing, cv2.MORPH_OPEN, kernel_open)
    
    bkgrnd_box = get_bkgrnd_box(closing)
    
    if len(bkgrnd_box) > 0:
        # perspective transform to original image size
        img_new, gray_new, closing_new = get_perspective_transform(bkgrnd_box, sh_img, img, gray, closing)
        # perspective transform to A4 size
        #scale = 20
        #sh_A4 = tuple([int(scale*A4_h),int(scale*A4_w)])
        #img_new, gray_new = get_perspective_transform(bkgrnd_box, sh_A4, img, gray)
    else:
        img_new = img
        gray_new = gray
        closing_new = closing
    
    closing_new = clean_up_boundary(closing_new)
    
    img_end = np.copy(img_new)
    img_start = np.copy(img_new)
    
    # get the peak grayscale value of the background
    hist = cv2.calcHist([gray_new], [0] , None, [255] , [0, 255])
    bkgrnd_color = np.argmax(hist)
    
    pruned_box, pruned_cntrs, pruned_hulls = get_pruned_cntrs(closing_new,gray_new,img_new)
    
    # write to .dxf file
    import ezdxf
    
    doc = ezdxf.new('R2000')
    msp = doc.modelspace()
    
    for c in pruned_cntrs:
        cv2.drawContours(img_start, [c], -1, (0,255,0), 4)
        # write to .dxf file
        points = np.squeeze(c)
        pts = list(map(tuple,points))
        lwp = msp.add_lwpolyline(pts)
        lwp.closed = True
        
    cv2.imwrite(fname+".jpg",img_start)
    
    
    ## use the active contour method in case there are shadows and we need to snap to the actual object
    ## start with the contours obtained above as initial estimates and then run the active contour method
    #get_snakes(pruned_hulls, pruned_cntrs, gray_new, img_end, img_start)
    #get_snakes(pruned_hulls, pruned_cntrs, closing_new, img_end, img_start)
    
    doc.saveas(fname+".dxf")
         
    #cv2.drawContours(im_bl,cnts,-1,(255,255,255),3)
    #output_image = os.path.join(folder_results,"Outline_"+fname+".jpg")
    #im_bl_image = os.path.join(folder_results,"Shapes_"+fname+".jpg")
    #box_image = os.path.join(folder_results,"Box_and_Contours_"+fname+".jpg")
    #cv2.imwrite(output_image,img)
    #cv2.imwrite(im_bl_image,im_bl)
    #cv2.imwrite(box_image,img_copy)
    
    
