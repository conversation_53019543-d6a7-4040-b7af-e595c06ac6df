# -*- coding: utf-8 -*-
"""
Created on Thu Jan 16 09:56:04 2020

@author: SK, RU
"""

########################## main file to process all the captured images ##############################

import numpy as np
import cv2
import imutils
import os

SIGMA = 0.33 # for Canny edges detection
THRESH_CONTOUR = 50 # was set to 250 originally
THRESH_BKGRND_MAX = 0.85 # back ground paper is at most 0.85 of the image area
THRESH_BKGRND_MIN = 0.40 # back ground paper is atleast 0.40 of the image area
THRESH_AREA_MIN = 0.001 # was set to 0.003 originally
THRESH_AREA_MAX = 0.50 # tool "area" is at most half the background paper


TOLERANCE_DIST = 40

THRESH_SLOPE = 1e-10
SLOPE_MAX = 1e10

TOLERANCE_BKGRND = 10

USE_SEGMENTATION = True

# size of A4 paper in mm
A4_h = 297
A4_w = 210


# perimeter length of the contour
def cntrperi(cnt):
   return cv2.arcLength(cnt,True)

# area of the contour
def cntrarea(cnt):
    return cv2.contourArea(cnt)

# get the area of a rectangular box
def get_area(box):
    h = np.linalg.norm(box[1]-box[2])
    w = np.linalg.norm(box[2]-box[3])
    
    return h*w

# Finds the intersection of two lines given in Hesse normal form. Returns closest integer pixel locations.
# See https://stackoverflow.com/a/383527/5087436
def intersection(line1, line2):

    rho1 = line1[0]; theta1 = line1[1]
    rho2 = line2[0]; theta2 = line2[1]
    A = np.array([
        [np.cos(theta1), np.sin(theta1)],
        [np.cos(theta2), np.sin(theta2)]
    ])
    b = np.array([[rho1], [rho2]])
    x0, y0 = np.linalg.solve(A, b)
    x0, y0 = int(np.round(x0)), int(np.round(y0))
    return np.array([x0, y0],dtype=np.int32)


def plot_lines(image, pruned_lines):
    #Display on the original image
    sh = image.shape
    for i in range(0,len(pruned_lines)):
       rho = pruned_lines[i][0]
       theta = pruned_lines[i][1]
       a = np.cos(theta)
       b = np.sin(theta)
         
       x0 = a*rho
       y0 = b*rho
       x1 = int(x0 + np.maximum(sh[0],sh[1])*(-b))
       y1 = int(y0 + np.maximum(sh[0],sh[1])*(a))
       x2 = int(x0 - np.maximum(sh[0],sh[1])*(-b))
       y2 = int(y0 - np.maximum(sh[0],sh[1])*(a))
       # overlay line on original image
       cv2.line(image,(x1,y1),(x2,y2),(0,255,0),2)

# "grab" the rectangle which encapsulates the background paper
def get_bkgrnd_box(image, sh_img):            
   
    if USE_SEGMENTATION == False:
        # get Hough lines to get the four boundary lines around the background paper
        #edges = cv2.Canny(image,100,200)
        #lines = cv2.HoughLines(edges,1,np.pi/180,threshold = int(np.minimum(sh_img[0],sh_img[1])/3.0))
            
        lines = cv2.HoughLines(image,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/3.0))    
        lines = np.squeeze(lines)
        
        # get only those lines which are either close to 0 deg slope or 90 deg slope
        pruned_lines = -10000*np.ones((lines.shape[0],lines.shape[1]),dtype=np.float32)
        k = 0
        for i in range(0,len(lines)):   
            rho = lines[i][0]
            theta = lines[i][1]
            a = np.cos(theta)
            b = np.sin(theta)
            if (np.abs(a) < 0.15 or np.abs(a) > 0.85): 
                #pruned_lines[k,0] = rho; pruned_lines[k,1] = theta
                # need to take care when rho is negative
                if rho > 0:
                    pruned_lines[k,0] = rho; pruned_lines[k,1] = theta
                else:
                    pruned_lines[k,0] = -1.0*rho; pruned_lines[k,1] = np.pi - theta
                k = k+1
    
        pruned_lines = pruned_lines[pruned_lines[:,1] > -10000]
        pruned_lines = np.float32(pruned_lines)
            
        # cluster to 4 centers to denote the 4 strong lines of the box surrounding backgrnd paper
        centers = []
        if len(pruned_lines) >= 4: 
            # cluster to get 4 strong lines 
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 50, 0.1)         
            ret,label,centers = cv2.kmeans(np.squeeze(pruned_lines),4,None,criteria,10,cv2.KMEANS_RANDOM_CENTERS)
           
    
        temp_bkgrnd_box = []        
        # if we find 4 centers then find the intersection of those lines and those are the 4 corner points of the background paper
        if len(centers) == 4:
            for i in range(0,len(centers)-1):         
                for j in range(i+1,len(centers)):
                    line1 = centers[i]; line2 = centers[j]
                    if np.abs(line1[1] - line2[1]) > 1e-2: # make sure they aren't parallel lines
                        a = intersection(line1, line2)
                        if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):
                            temp_bkgrnd_box.append(a)        
        
        # convert to list of numbers
        if (len(temp_bkgrnd_box) > 0):
            a = np.asarray(temp_bkgrnd_box,dtype=np.int64)
            b = []
            b.append(a)
            temp_bkgrnd_box = b
    
    # but the paper might not be parallel to the axis of the image 
    # then use findContours to get the background paper        
    im2, cnts, hier = cv2.findContours(image,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrperi, reverse = True)[:20]
    
    # blank image to draw the big contour
    img_bl =  np.zeros((sh_img[0],sh_img[1]),dtype = "uint8")
    bkgrnd_box = []
    for c in cnts:
    	# if the contour has small perimeter, don't draw it
        if cntrperi(c) > THRESH_CONTOUR:
            rect = cv2.minAreaRect(c)
            box = cv2.boxPoints(rect)
            box = np.int0(box)              
            # get the rectangle which is between 0.6 and 0.8 of the area of the image; that should mostly be the background paper
            if (get_area(box)/(sh_img[0]*sh_img[1]) >= THRESH_BKGRND_MIN) and (get_area(box)/(sh_img[0]*sh_img[1]) <= THRESH_BKGRND_MAX):          
                #print(get_area(box)/(sh_img[0]*sh_img[1]))
                
                epsilon = 0.075*cv2.arcLength(c,True)
                approx = cv2.approxPolyDP(c,epsilon,True)
                rect = cv2.minAreaRect(approx)
                box = cv2.boxPoints(rect)
                box = np.int0(box) 
                #print('arc len:= %f' % cv2.arcLength(c,True))
                #print(c)
                #print(approx)
                #cv2.drawContours(img_copy, [box], -1, (0,255,0), 2)
                #cv2.drawContours(img_copy, [approx], -1, (0,0,255), 2)                
                bkgrnd_box.append(box)
                cv2.drawContours(img_bl,[c],0,(255,255,255),4)  
                #cv2.drawContours(gray_copy,[box],0,(0,255,0),4)  

    
    # use Hough lines on the image with only the contour
    lines = cv2.HoughLines(img_bl,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/2.0))    
    lines = np.squeeze(lines)

    # cluster to 4 centers to denote the 4 strong lines of the box surrounding backgrnd paper
    centers = []
    if len(lines) >= 4: 
        # first check for "negative" rho and adjust things appropriately
        for i in range(0,len(lines)):   
            rho = lines[i][0]
            theta = lines[i][1]
            a = np.cos(theta)
            b = np.sin(theta)                            
            if rho < 0:                
                if np.abs(np.pi - theta) <= 0.035:
                    lines[i][0] = -1.0*rho; lines[i][1] = np.pi - theta
        # cluster to get 4 strong lines         
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 50, 0.01)         
        ret,label,centers = cv2.kmeans(np.squeeze(lines),4,None,criteria,10,cv2.KMEANS_RANDOM_CENTERS)
       

    temp_bkgrnd_box = []        
    # if we find 4 centers then find the intersection of those lines and those are the 4 corner points of the background paper
    if len(centers) == 4:
        for i in range(0,len(centers)-1):         
            for j in range(i+1,len(centers)):
                line1 = centers[i]; line2 = centers[j]
                if np.abs(line1[1] - line2[1]) > 1e-2: # make sure they aren't parallel lines
                    a = intersection(line1, line2)
                    if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):
                        temp_bkgrnd_box.append(a)        
    
    # convert to list of numbers
    if (len(temp_bkgrnd_box) > 0):
        a = np.asarray(temp_bkgrnd_box,dtype=np.int64)
        b = []
        b.append(a)
        temp_bkgrnd_box = b

    # if hough line process gave 4 corners, use that otherwise stick to original to avoid crashing
    if (len(temp_bkgrnd_box) > 0):
        if (len(temp_bkgrnd_box[0]) == 4):
            bkgrnd_box = temp_bkgrnd_box
        
        
    if USE_SEGMENTATION == False:
        # refine the bkgrnd_box with the temp_bkgrnd_box obtained previously from the Hough lines if that exists
        if len(temp_bkgrnd_box) > 0:
            if len(temp_bkgrnd_box[0]) == 4:
                for i in range(0,len(bkgrnd_box[0])):
                    a = bkgrnd_box[0][i]
                    b = temp_bkgrnd_box[0][0]
                    dist = np.sqrt((a[0]-b[0])*(a[0]-b[0])+ (a[1]-b[1])*(a[1]-b[1]))            
                    ind = 0
                    for j in range(1,len(temp_bkgrnd_box[0])):
                        b = temp_bkgrnd_box[0][j]
                        if (np.sqrt((a[0]-b[0])*(a[0]-b[0])+ (a[1]-b[1])*(a[1]-b[1])) < dist):
                            dist = np.sqrt((a[0]-b[0])*(a[0]-b[0])+ (a[1]-b[1])*(a[1]-b[1]))
                            ind = j           
                            
                    b = temp_bkgrnd_box[0][ind]                               
                    if a[0] <= sh_img[1]/2:
                        bkgrnd_box[0][i,0] = np.maximum(a[0],b[0])
                    else: 
                        bkgrnd_box[0][i,0] = np.minimum(a[0],b[0])
                    if a[1] <= sh_img[0]/2:
                        bkgrnd_box[0][i,1] = np.maximum(a[1],b[1])
                    else:
                        bkgrnd_box[0][i,1] = np.minimum(a[1],b[1])                
    
    return bkgrnd_box

# perscpective transformation 
def get_perspective_transform(bkgrnd_box, sh_src, sh_dst, img, gray):                  
    #pts2 = np.float32([[0,0],[sh_img[1],0],[sh_img[1],sh_img[0]],[0,sh_img[0]]])
    #pts2 = np.float32([[0,0],[int(scale*A4_w),0],[int(scale*A4_w),int(scale*A4_h)],[0,int(scale*A4_h)]])
    
    pts2 = np.float32([[0,0],[sh_src[1],0],[sh_src[1],sh_src[0]],[0,sh_src[0]]])
    pts1 = -1*np.ones(np.shape(pts2))
    for i in range(0,4):
        d = 100000
        for j in range(0,len(bkgrnd_box[0])):        
            if np.linalg.norm(pts2[i]-bkgrnd_box[0][j]) <= d:
                d = np.linalg.norm(pts2[i]-bkgrnd_box[0][j])
                pts1[i,:] = bkgrnd_box[0][j]
                
    pts1 = np.float32(pts1)
    pts2 = np.float32([[0,0],[sh_dst[1],0],[sh_dst[1],sh_dst[0]],[0,sh_dst[0]]])
    # rect height and width
    #width = np.linalg.norm(pts1[0]-pts1[1])
    #height = np.linalg.norm(pts1[1]-pts1[2])
    M = cv2.getPerspectiveTransform(pts1,pts2)
    #gray_new = cv2.warpPerspective(gray,M,(sh_img[1],sh_img[0]))
    #img_new = cv2.warpPerspective(img,M,(sh_img[1],sh_img[0]))
    #gray_new = cv2.warpPerspective(gray,M,(int(scale*A4_w),int(scale*A4_h)))
    #img_new = cv2.warpPerspective(img,M,(int(scale*A4_w),int(scale*A4_h)))
    gray_new = cv2.warpPerspective(gray,M,(sh_dst[1],sh_dst[0]))
    img_new = cv2.warpPerspective(img,M,(sh_dst[1],sh_dst[0]))
    #closing_new = cv2.warpPerspective(img_closing,M,(sh_dst[1],sh_dst[0]))
    
    return img_new, gray_new


# for images where object has a little part beyond the background paper we cleanup the boundary
def clean_up_boundary(im):
    # just make the pixels at the boundary black
    THRESH_BNDRY = 9 # this changes to 9 for IMG_5015
    im[0:THRESH_BNDRY, :] = 0
    im[im.shape[0]-THRESH_BNDRY:im.shape[0], :] = 0
    im[im.shape[0]-THRESH_BNDRY:im.shape[0], :] = 0
    im[:,0:THRESH_BNDRY] = 0
    im[:,im.shape[1]-THRESH_BNDRY:im.shape[1]] = 0
    
    return im

# given box1 and box2, check if one is inside the other
def check_box_within_box(box1,box2):
    # output: 1 if box2 is within box1; 2 if box1 is within box2; 0 else
    area1 = get_area(box1); area2 = get_area(box2)
    h1 = np.linalg.norm(box1[1]-box1[2])
    w1 = np.linalg.norm(box1[2]-box1[3])
    h2 = np.linalg.norm(box2[1]-box2[2])
    w2 = np.linalg.norm(box2[2]-box2[3])
    flag = 0
    if area1 >= area2:
        l1 = box1[0] - box1[1];  l2 = box1[1] - box1[2]; l3 = box1[2] - box1[3]; l4 = box1[3] - box1[0];
        for j in range(0,len(box2)):
            d1 = np.cross(l1,box1[0]-box2[j])/np.linalg.norm(l1);
            d2 = np.cross(l3,box1[2]-box2[j])/np.linalg.norm(l3);
            d3 = np.cross(l2,box1[1]-box2[j])/np.linalg.norm(l2);
            d4 = np.cross(l4,box1[3]-box2[j])/np.linalg.norm(l4);
            da = np.abs(d1)+np.abs(d2); db = np.abs(d3)+np.abs(d4)
            if (np.abs(da - h1) <= TOLERANCE_DIST) and (np.abs(db - w1) <= TOLERANCE_DIST):
                flag = 1
            else:
                flag = 0
                break
    else:
        l1 = box2[0] - box2[1];  l2 = box2[1] - box2[2]; l3 = box2[2] - box2[3]; l4 = box2[3] - box2[0];
        for j in range(0,len(box1)):
            d1 = np.cross(l1,box2[0]-box1[j])/np.linalg.norm(l1);
            d2 = np.cross(l3,box2[2]-box1[j])/np.linalg.norm(l3);
            d3 = np.cross(l2,box2[1]-box1[j])/np.linalg.norm(l2);
            d4 = np.cross(l4,box2[3]-box1[j])/np.linalg.norm(l4);
            da = np.abs(d1)+np.abs(d2); db = np.abs(d3)+np.abs(d4)
            if (np.abs(da - h2) <= TOLERANCE_DIST) and (np.abs(db - w2) <= TOLERANCE_DIST):
                flag = 2   
            else:
                flag = 0
                break
        
    return flag

# remove boxes within a box
def remove_inner_boxes(rect_box, good_box, all_hulls, candidate_cntrs, img_copy):
    pruned_box = [] # remove boxes which are within a box and prune the original list of boxes
    pruned_hulls = []
    pruned_cntrs = []
    # consider every pair of boxes and check if one box is within the other
    for i in range(0,len(rect_box)):
        if good_box[i] == True:
            for j in range(0,len(rect_box)):
                if i != j:
                    flag = check_box_within_box(rect_box[i],rect_box[j])
                    if flag == 1: # j is within i
                        good_box[j] = False
                    if flag == 2: # i is within j
                        good_box[i] = False
                        break       
    for i in range(0,len(rect_box)):
        if good_box[i] == True:                
            pruned_box.append(rect_box[i])
            pruned_hulls.append(all_hulls[i])
            pruned_cntrs.append(candidate_cntrs[i])
            cv2.drawContours(img_copy,[rect_box[i]],0,(0,255,0),4)
            cv2.drawContours(img_copy, [all_hulls[i]], -1, (0,0,255), 4) 
    return pruned_box, pruned_hulls, pruned_cntrs

# find_contours followed by pruning the contours
def get_pruned_cntrs(image,image_orig):
    
    img_copy = np.copy(image_orig)
    sh = image_orig.shape
    # get contours around the segmented objects
    im2, cnts, hier = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrperi, reverse = True)[:20] #sort the top 20
    
    rect_box = [] # min rect around a contour
    good_box = [] # good_box = true implies a box is not consumed by any another box 
    all_hulls = [] # list of convex hull around contour
    candidate_cntrs = [] # contours which are neither too big nor too small
    for c in cnts:
    	# if the contour is small perimeter, don't draw it
        if cntrperi(c) > THRESH_CONTOUR:           
            #cv2.drawContours(image_orig, [c], -1, (0,255,0), 4)  
            rect = cv2.minAreaRect(c)
            #print(rect)
            box = cv2.boxPoints(rect)
            box = np.int0(box)      
            cv2.drawContours(image_orig, [box], -1, (0,255,0), 4)  
            #print(get_area(box)/(sh[0]*sh[1]))
            #print(box)
            # remove very large rectangles (the background paper if it exists) and very small rectangles (just noise)
            if (get_area(box)/(sh[0]*sh[1]) >= THRESH_AREA_MIN) and (get_area(box)/(sh[0]*sh[1]) <= THRESH_AREA_MAX):          
                rect_box.append(box)
                good_box.append(True) # initially assume that all boxes are good. they will be refined later
                candidate_cntrs.append(c)
                cv2.drawContours(image_orig, [c], -1, (255,0,255), 3) 
                #print(box)
                #print(c)
                hull = cv2.convexHull(c)
                all_hulls.append(hull)
                cv2.drawContours(image_orig, [hull], -1, (0,0,255), 4) 
    
    # prune things, i.e., remove contours-within-contours/boxes-within-boxes      
    pruned_box, pruned_hulls, pruned_cntrs = remove_inner_boxes(rect_box, good_box, all_hulls, candidate_cntrs, img_copy)
    
    return pruned_box, pruned_cntrs, pruned_hulls


# get the largest contour in terms of area enclosed
def get_largest_cntr(pruned_cntrs):
        
    max_area = cntrarea(pruned_cntrs[0])
    if len(pruned_cntrs) == 1:
        return pruned_cntrs
    else:
        for c in pruned_cntrs:
            if cntrarea(c) >= max_area:
                largest_cntr = c
                max_area = cntrarea(c)
                #print(max_area)
    return [largest_cntr]
    

# get the contours for each object in the image
def get_outlines(im):

    a = im.shape[0]
    b = im.shape[1]
    # resize image to 640 rows for faster processing; this low res also makes several CV algos perform better
    a_new = 640
    
    img = cv2.resize(im,(int(a_new*b/a),a_new))
    sh_img = img.shape
    # if image in landscape convert to portrait

    if sh_img[0] <= sh_img[1]:    
        img = imutils.rotate_bound(img, 90)

    sh_img = img.shape    

    # get the grayscale image
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # use either segmentated image or edge image for further processing 
    if USE_SEGMENTATION:
        # simple thresholding for segmentation        
        ret,th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        #img_seg = cv2.bitwise_not(th)        
        # "opening" transformation
        # kernel for "opening" transformation 
        kernel_open = np.ones((7,7),np.uint8)
        img_for_bkgrnd = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open)
        #opening = cv2.morphologyEx(closing, cv2.MORPH_OPEN, kernel_open)
    else:
        # gaussian blur to remove spurious edges
        blur = cv2.GaussianBlur(gray,(3,3),0) # originally 3 was used
        # compute the median of the single channel pixel intensities
        v = np.median(blur)
        # apply automatic Canny edge detection using the computed median
        lower = int(max(0, (1.0 - SIGMA) * v))
        upper = int(min(255, (1.0 + SIGMA) * v))
        #lower = 100
        #upper = 200
        edges = cv2.Canny(blur,lower,upper)    
        # "closing" transformation        
        # kernel for "closing" transformation 
        kernel_close = np.ones((7,7),np.uint8)
        img_for_bkgrnd = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)        
        
    # get the box around the background paper
    bkgrnd_box = get_bkgrnd_box(img_for_bkgrnd, sh_img)    
   
    if len(bkgrnd_box) > 0:      
        img_new, gray_new = get_perspective_transform(bkgrnd_box, sh_img, sh_img, img, gray)#, img_for_bkgrnd)   
        blur = cv2.GaussianBlur(gray_new,(3,3),0) # may or may not use Gaussian smoothing
        edges = cv2.Canny(blur,100,200)    
        # "closing" transformation        
        # kernel for "closing" transformation 
        kernel_close = np.ones((7,7),np.uint8)
        img_for_tool_outline = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)
    else:
        # back ground paper not found. what is to be done in this case is not clear!!
        img_new = img
        gray_new = gray
        blur = cv2.GaussianBlur(gray_new,(3,3),0) # may or may not use Gaussian smoothing
        edges = cv2.Canny(blur,100,200)    
        # "closing" transformation        
        # kernel for "closing" transformation 
        kernel_close = np.ones((7,7),np.uint8)
        img_for_tool_outline = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)
        
    
    #img_for_tool_outline = clean_up_boundary(img_for_tool_outline)
    # further smooth by filtering
    #closing_new = cv2.medianBlur(closing_new,3)
    #closing_new = cv2.GaussianBlur(closing_new,(3,3),0)
    
    transformed_img = np.copy(img_new)
    transformed_img_with_cntrs_hulls = np.copy(img_new)

    pruned_box, pruned_cntrs, pruned_hulls = get_pruned_cntrs(img_for_tool_outline,img_new)
    
    # get the contour of largest enclosed area 
    pruned_cntrs = get_largest_cntr(pruned_cntrs) # should have ideally only one contour fittng the tool
    
    myhulls = []
    mycntrs = []
    myoffsetX = []
    myoffsetY = []    
    for c in pruned_cntrs:     
        # get the convex hull
        hull = cv2.convexHull(c)  
        
        # draw the contours and hulls in an image to make sure things are going okay
        cv2.drawContours(transformed_img_with_cntrs_hulls, [c], -1, (0,255,0), 2) 
        cv2.drawContours(transformed_img_with_cntrs_hulls, [hull], -1, (255,0,255), 2)
        
        points = np.squeeze(c)
        points = points.astype(float)
        x_min = np.min(points[:,0])
        y_min = np.min(points[:,1])
        hull_points = np.squeeze(hull)
        hull_points = hull_points.astype(float)
        
        # scale to the A4 paper size and flatten them so that we output a 1-dim array
        cntr_pts = []
        hull_pts = []
        for i in range(0,len(points[:,0])):
            points[i,0] = round(float(points[i,0] - x_min)*float(A4_w/sh_img[1]),2)
            points[i,1] = round(float(points[i,1] - y_min)*float(A4_h/sh_img[0]),2)
            # convert numpy values to naive Python types; this is needed while transmitting back to the client
            cntr_pts.append(points[i,0].item())
            cntr_pts.append(points[i,1].item())
        for i in range(0,len(hull_points[:,0])):            
            hull_points[i,0] = round(float(hull_points[i,0] - x_min)*float(A4_w/sh_img[1]),2)        
            hull_points[i,1] = round(float(hull_points[i,1] - y_min)*float(A4_h/sh_img[0]),2)
            # convert numpy values to naive Python types; this is needed while transmitting back to the client
            hull_pts.append(hull_points[i,0].item())
            hull_pts.append(hull_points[i,1].item())
            
        # append the first point at the end to close the loop
        cntr_pts.append(points[0,0].item())
        cntr_pts.append(points[0,1].item())
        hull_pts.append(hull_points[0,0].item())
        hull_pts.append(hull_points[0,1].item())
        
        # create a list of the cntrs & hulls etc. if there are multiple objects in the image
        mycntrs.append(cntr_pts)
        myhulls.append(hull_pts)
        myoffsetX.append(x_min.item())
        myoffsetY.append(y_min.item())


    cv2.imwrite("output.jpg",transformed_img_with_cntrs_hulls)
    
    return transformed_img, transformed_img, mycntrs, myhulls, myoffsetX, myoffsetY
        
        
################# main sim to process an entire folder of images and generate .jpg and .dxf for each file ################

if __name__ == '__main__':
    
    # folders for all the input data and output results
    folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Original Images//Test_input'#Tool Library jpg' 
    #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Original Images//Tool Library jpg' 
    folder_jpg_results = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Test_output'
    for fname in os.listdir(folder_jpg):        
        if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"): 
            print(fname) 
            im = cv2.imread(os.path.join(folder_jpg,fname))
            cvImagewithCntrs, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY = get_outlines(im)    
            cv2.imwrite(os.path.join(folder_jpg_results,fname),cvImagewithCntrs)
        else:
            print("Error: Need input files to be in JPG format")


