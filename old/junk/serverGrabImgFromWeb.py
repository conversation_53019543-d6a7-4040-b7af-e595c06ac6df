from flask import Flask, request, Response, render_template
from flask_cors import CORS
import jsonpickle
import numpy as np
import cv2
import base64
import io
from PIL import Image
#import main
import grabImgFromWeb
#postgres library
import psycopg2
from datetime import datetime
import json
import string
import random
import os, boto3
from botocore.exceptions import NoCredentialsError
import time


# postgres database environment variable
if grabImgFromWeb.MYENVIRONMENT == 'HEROKU':
    DATABASE_URL = os.environ['DATABASE_URL']

# Amazon S3 access
if grabImgFromWeb.MYENVIRONMENT == 'HEROKU':
    S3_BUCKET_NAME = os.environ.get('S3_BUCKET_NAME')

# take in base64 string and return PIL image
def stringToImage(base64_string):
    imgdata = base64.b64decode(base64_string)
    return Image.open(io.BytesIO(imgdata))

# convert PIL Image to an RGB image (technically a numpy array) that's compatible with opencv
def toRGB(image):
    return cv2.cvtColor(np.array(image), cv2.COLOR_BGR2RGB)

# function to upload file to S3
def upload_to_aws(local_file, bucket, s3_file):
    #s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    s3 = boto3.client('s3')
    try:
        s3.upload_file(local_file, bucket, s3_file)
        print("Upload Successful")
        return True
    except FileNotFoundError:
        print("The file was not found")
        return False
    except NoCredentialsError:
        print("Credentials not available")
        return False

# Initialize the Flask application
app = Flask(__name__)
cors = CORS(app, resources={r"/api/*": {"origins": "*"}})

# route http posts to this method
@app.route('/api/sendImg', methods=['POST'])
def sendImg ():
    r = request # this is the incoming request    
    jsondata = r.get_json()    
    print("toolName:= ", jsondata["toolName"])    

    # generate a random string for filename to be stored in database and upload to S3
    randomFilename = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
    randomFilename += '.jpg'

    # get tool name
    toolName = jsondata["toolName"]
    # get browser type
    browser = jsondata["userAgent"]

    # in incoming string is encoded as b64  
    b64_string = jsondata["img"]
    # convert this first into a PIL image
    image = stringToImage(b64_string)
    # convert into opencv compatible format
    cvImage = toRGB(image)
    # write to a file to be uploaded to S3
    print('randomfilename:-', randomFilename)
    cv2.imwrite(randomFilename,cvImage)    

    # image/ml processing to get the contours/outlines/convex hull    
    cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY = grabImgFromWeb.get_contours(cvImage)
    #cv2.imwrite('after_python_processing.jpg',cvImage)
    
    # now convert back into b64 format
    jpegImage = cv2.imencode('.jpg', cvImage)[1].tostring()
    b64Image = base64.b64encode(jpegImage)

    # response is in json format to be sent back to javascript file
    response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata}

    # encode response using jsonpickle
    response_pickled = jsonpickle.encode(response)

    # write things in the postgres database on Heroku    
    if grabImgFromWeb.MYENVIRONMENT == 'HEROKU':        
        # connect to the database
        
        # for localhost server and local database
        #con = psycopg2.connect(host="localhost", database="mydb", user="postgres", password="Kudekar", port=5432)
        
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
        
        # cursor
        cur = con.cursor()
    
        # insert into database
        now = datetime.now() # current date and time
        date_time = now.strftime("%m/%d/%Y, %H:%M:%S")
        
        # for localhost server and local database
        #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
        
        cur.execute("insert into userDataDB (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
    
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close() 
    
        # upload to S3    
        uploaded = upload_to_aws(randomFilename, S3_BUCKET_NAME, 'public/'+randomFilename)
    
        # delete files *.jpg which are older than 10 minutes
        ten_minutes_ago = time.time() - 600
        for fname in os.listdir('.'):
            if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"):
                st=os.stat(fname)
                mtime=st.st_mtime
                if mtime < ten_minutes_ago:
                    print('removed %s'%fname)
                    os.remove(fname)

    return Response(response=response_pickled, status=200, mimetype="application/json")

@app.route('/')
def index():
    return render_template('index.html')

# start flask app
if __name__ == '__main__':
    
    if grabImgFromWeb.MYENVIRONMENT == 'HEROKU':
        #app.use(express.static(path.join(__dirname, 'app/templates')))
        app.run()   
    else:
        app.run(host="127.0.0.1", port=5000)
