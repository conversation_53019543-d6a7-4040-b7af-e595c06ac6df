from flask import Flask, request, Response
from flask_cors import CORS
import j<PERSON>pickle
import numpy as np
import cv2
import base64
import io
from PIL import Image
import imgProcFunctions

# take in base64 string and return PIL image
def stringToImage(base64_string):
    imgdata = base64.b64decode(base64_string)
    return Image.open(io.BytesIO(imgdata))

# convert PIL Image to an RGB image (technically a numpy array) that's compatible with opencv
def toRGB(image):
    return cv2.cvtColor(np.array(image), cv2.COLOR_BGR2RGB)

# Initialize the Flask application
app = Flask(__name__)
cors = CORS(app, resources={r"/api/*": {"origins": "*"}})

# route http posts to this method
@app.route('/api/sendImg', methods=['POST'])
def sendImg ():
    r = request # this is the incoming request

    # in incoming string is encoded as b64  
    b64_string = r.data
    # convert this first into a PIL image
    image = stringToImage(b64_string)
    # convert into opencv compatible format
    cvImage = toRGB(image)

    # do some fancy processing here ...
    # right now we just flip the image
    #cvImage = cv2.flip(cvImage,0) 
    cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY = imgProcFunctions.get_contours(cvImage)
    
    # now convert back into b64 format
    jpegImage = cv2.imencode('.jpg', cvImage)[1].tostring()
    b64Image = base64.b64encode(jpegImage)

    # response is in json format     
    response = {'jpeg': '{}'.format(b64Image), 'offsetX': myoffsetX[0], 'offsetY': myoffsetY[0], 'convexhull': myconvexhull[0],'rawdata': myrawdata[0]}

    # encode response using jsonpickle
    response_pickled = jsonpickle.encode(response)

    return Response(response=response_pickled, status=200, mimetype="application/json")

# start flask app
app.run(host="127.0.0.1", port=5000)
