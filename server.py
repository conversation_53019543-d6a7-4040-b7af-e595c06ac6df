from flask import Flask, request, Response, render_template
from flask_cors import CORS
import jsonpickle
import numpy as np
import cv2
import base64
import io
from PIL import Image
#import main
import imgProcFunctions_v3
#postgres library
import psycopg2
from datetime import datetime
import json
import string
import random
import os, boto3
from botocore.exceptions import NoCredentialsError
import time
import sys
import math
from botocore.exceptions import ClientError



# postgres database environment variable
if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
    DATABASE_URL = os.environ['DATABASE_URL']

# Amazon S3 access
if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
    S3_BUCKET_NAME = os.environ.get('S3_BUCKET_NAME')

# take in base64 string and return PIL image
def stringToImage(base64_string):
    imgdata = base64.b64decode(base64_string)
    return Image.open(io.BytesIO(imgdata))

# convert PIL Image to an RGB image (technically a numpy array) that's compatible with opencv
def toRGB(image):
    return cv2.cvtColor(np.array(image), cv2.COLOR_BGR2RGB)

# function to upload file to S3
def upload_to_aws(local_file, bucket, s3_file):
    #s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    s3 = boto3.client('s3')
    try:
        s3.upload_file(local_file, bucket, s3_file)
        print("Upload Successful")
        return True
    except FileNotFoundError:
        print("The file was not found")
        return False
    except NoCredentialsError:
        print("Credentials not available")
        return False
    
# function to download file from S3 and save to local_file
def download_from_aws(local_file, bucket, s3_file):
    #s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    s3 = boto3.client('s3')
    try:
        s3.download_file(bucket, s3_file, local_file)
        print("Download Successful")
        return True
    except FileNotFoundError:
        print("The file was not found")
        return False
    except NoCredentialsError:
        print("Credentials not available")
        return False

# check if file exists on s3
def check(bucket, key):
    s3 = boto3.client('s3')
    try:
        s3.head_object(Bucket=bucket, Key=key)
    except ClientError as e:
        return int(e.response['Error']['Code']) != 404
    return True

# function to delete a file from S3
def delete_s3_file(bucket, s3_file):    
    s3 = boto3.client('s3')
    s3.delete_object(Bucket=bucket, Key=s3_file)
    print("Deleting %s" % s3_file)    

# Initialize the Flask application
app = Flask(__name__)
app.config['DEBUG'] = True  # Enable debug mode
cors = CORS(app, resources={r"/api/*": {"origins": "*"}})

# route http posts to this method
@app.route('/api/sendImg', methods=['POST'])
def sendImg ():
    r = request # this is the incoming request    
    jsondata = r.get_json() 
    #print("jsondata:= ", jsondata)
    # get task type
    task = jsondata["task"]
            
    if task == "processPhoto" or task == "processPhotoFromEditor":

        # check if there is userEmail field
        if "userEmail" in jsondata:
            print('userEmail from processPhoto task:=', jsondata["userEmail"])
        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]
        print("fileName:= ", jsondata["fileName"])    
        
        # get FOV data
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            fov = jsondata["FOV"]
            ZoomFactor = jsondata["ZoomFactor"]
        else:
            fov = 57.72
            ZoomFactor = 1.0
        # generate a random string for filename to be stored in database and upload to S3
        photoID = ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))
        #photoID += '.jpg'
    
        # get filename name
        fileName = jsondata["fileName"]
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"]
        # get toolboxnumber if it exists
        toolboxNumber = 1
        if "toolboxNumber" in jsondata:
            toolboxNumber = jsondata["toolboxNumber"]
        
        # default backgroundPaperSize for appVersionNumber = 1.1.0 is A4
        # default backgroundPaperSize when upload from local disk is Letter
        backgroundPaperSize = "Letter"
        if task == "processPhoto":
            # get appVersionNumber
            appVersionNumber = jsondata["appVersionNumber"] 
            # get the backgroundPaperSize                
            if appVersionNumber >= "1.2.0":            
                backgroundPaperSize = jsondata["backgroundPaperSize"]
            if appVersionNumber == "1.1.0":            
                backgroundPaperSize = "A4"
            
        # in incoming string is encoded as b64  
        b64_string = jsondata["img"]
        # convert this first into a PIL image
        image = stringToImage(b64_string)
        # convert into opencv compatible format
        cvImage = toRGB(image)
        # write to a file to be uploaded to S3
        print('photoID:-', photoID)
        cv2.imwrite(photoID+'.jpg',cvImage)   
        
        # upload to S3    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            uploaded = upload_to_aws(photoID+'.jpg', S3_BUCKET_NAME, 'public/'+photoID+'.jpg')
        
        rows = cvImage.shape[0]
        columns = cvImage.shape[1]
    
        # image/ml processing to get the contours/outlines/convex hull    
        if imgProcFunctions_v3.MYENVIRONMENT == 'CRAWL':
            cvImageHighRes, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, myPaperSize, myWidthInmm, myHeightInmm, cvImageWithOutlines, errorMessage = imgProcFunctions_v3.proc_img_from_web(cvImage)
        else:
            cvImageHighRes, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, myPaperSize, myWidthInmm, myHeightInmm, cvImageWithOutlines, errorMessage = imgProcFunctions_v3.get_contours(cvImage, fov, ZoomFactor, backgroundPaperSize)            
        #cv2.imwrite('after_python_processing.jpg',cvImage)
                
        # now convert back into b64 format
        # if the task is from the editor 
        if task == "processPhoto":            
            jpegImage = cv2.imencode('.jpg', cvImage)[1].tostring()
        else:
            jpegImage = cv2.imencode('.jpg', cvImageHighRes)[1].tostring()
            
        b64Image = base64.b64encode(jpegImage)
        jpegImageWithOutlines = cv2.imencode('.jpg', cvImageWithOutlines)[1].tostring()
        b64ImageWithOutlines = base64.b64encode(jpegImageWithOutlines)
        
        # response is in json format to be sent back to javascript file
        response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'jpegWithOutlines': '{}'.format(b64ImageWithOutlines), 'photoID': photoID, 'errorMessage': errorMessage, 'toolboxNumber': toolboxNumber}
        print("errorMessage:=", errorMessage)

        # providing hooks for object recognition message to iPhone from pythonKaiser
        # right now we just have an empty message and the actual errorMessage
        if task == "processPhoto":
            if appVersionNumber >= "1.2.0":            
                if errorMessage == "":
                    type = "regular"
                    errorMessage = "Looking fabulous! Swipe to check the next tool. [Paper size: " + myPaperSize + "]"
                else:
                    type = "error"
                response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'jpegWithOutlines': '{}'.format(b64ImageWithOutlines), 'photoID': photoID, 'message': errorMessage, 'messageType': type, 'toolboxNumber': toolboxNumber}                
                    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
    
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':        
            # connect to the database
            
            # for localhost server and local database
            #con = psycopg2.connect(host="localhost", database="mydb", user="postgres", password="Kudekar", port=5432)
            
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            #cur.execute("insert into userDataDB (toolKaiserid, photoid, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, photoID, fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor))  
            cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize, toolboxnumber) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, photoID, 'p', fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor, backgroundPaperSize, toolboxNumber))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.jpg which are older than 10 minutes
            ten_minutes_ago = time.time() - 600
            for fname in os.listdir('.'):
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "processToolKaiserID":       
        # get all the photoIDs from the database which match the toolKaiserID
        
        # response is in json format to be sent back to javascript file
        toolKaiserID = jsondata["toolKaiserID"]
        
        # get the startDate to fetch the images from 
        startDate = jsondata["startDate"]
        print('startDate:= ', startDate)
    
        # connect to the database
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
        # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs starting from startDate till today 
        #cur.execute("SELECT photoid FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+"CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)") 
        cur.execute("SELECT id FROM userDataDB WHERE status=\'active\' AND toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+ "type = \'p\'" + " AND "+ "CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)")         
        
        # this is a list of tuples (rownumber, photoID, backgroundpapersize)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
            
        photoIDs = []        
        for r in rows:
            #print(type(r[0]), r[0])
            photoIDs.append(str(r[0]))            
            
        # use the toolKaiserID to get data from postgres
        response = {'photoIDs': photoIDs}    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "processPhotoID":
        
        # generate a random string for filename to be stored in database and upload to S3
        photoID = jsondata["photoID"]
        print("photoID:= ", photoID) 
        
        ### connect to the database and pull the backgroundPaperSize associated to the photoID
        
        # connect to the database
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
        # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs starting from startDate till today 
        #cur.execute("SELECT photoid FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+"CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)") 
        cur.execute("SELECT backgroundpapersize, toolboxnumber FROM userDataDB WHERE id = "+"\'"+photoID+"\'")         
        
        # this is a list of tuples (rownumber, photoID, backgroundpapersize)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
            
        backgroundPaperSize = [] 
        #toolboxNumber = []
        for r in rows:
            #print(type(r[0]), r[0])
            backgroundPaperSize.append(str(r[0])) 
            toolboxNumber = r[1]
        
        # we download the corresponding file from AWS bucket and call processPhoto
        downloaded = download_from_aws('cvImage.jpg', S3_BUCKET_NAME, 'public/'+photoID+'.jpg')
        cvImage = cv2.imread('cvImage.jpg')    
    
        fov = 0
        ZoomFactor = 1.0
        # image/ml processing to get the contours/outlines/convex hull    
        if imgProcFunctions_v3.MYENVIRONMENT == 'CRAWL':
            cvImageHighRes, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, myPaperSize, myWidthInmm, myHeightInmm, cvImageWithOutlines, errorMessage = imgProcFunctions_v3.proc_img_from_web(cvImage)
        else:
            cvImageHighRes, cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, myPaperSize, myWidthInmm, myHeightInmm, cvImageWithOutlines, errorMessage = imgProcFunctions_v3.get_contours(cvImage, fov, ZoomFactor, backgroundPaperSize[0])            
        #cv2.imwrite('after_python_processing.jpg',cvImage)
        
        # now convert back into b64 format
        jpegImage = cv2.imencode('.jpg', cvImageHighRes)[1].tostring()
        b64Image = base64.b64encode(jpegImage)
        jpegImageWithOutlines = cv2.imencode('.jpg', cvImageWithOutlines)[1].tostring()
        b64ImageWithOutlines = base64.b64encode(jpegImageWithOutlines)

        # connect to the database to get the filename corresponding to the photoID
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
        # cursor
        cur = con.cursor()
        
        #cur.execute("SELECT filename FROM userDataDB WHERE photoid = "+"\'"+photoID+"\'") 
        cur.execute("SELECT filename FROM userDataDB WHERE id = "+"\'"+photoID+"\'") 
        
        # this is a list of tuples (rownumber, photoID)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                            
        #print(type(r[0]), r[0])
        fileName = str(rows[0][0])
        print('fileName:= ', fileName)
        
        #if fileName == "":            
        fileName = str(photoID)
        print('fileName:= ', fileName)

        # response is in json format to be sent back to javascript file
        if "withOutlines" in jsondata:
            if jsondata["withOutlines"] == True:
                response = {'jpegWithOutlines': '{}'.format(b64ImageWithOutlines), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'fileName': fileName, 'errorMessage': errorMessage, 'toolboxNumber': toolboxNumber}        
            else:
                response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'fileName': fileName, 'errorMessage': errorMessage, 'toolboxNumber': toolboxNumber}
        else:
            response = {'jpeg': '{}'.format(b64Image), 'numOfTools': len(myoffsetX), 'offsetX': myoffsetX, 'offsetY': myoffsetY, 'convexhull': myconvexhull,'rawdata': myrawdata, 'paperSize': myPaperSize, 'myWidthInmm': myWidthInmm, 'myHeightInmm': myHeightInmm, 'fileName': fileName, 'errorMessage': errorMessage, 'toolboxNumber': toolboxNumber}
            
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)

        # remove any .jpg files which are older than 10 minutes            
        ten_minutes_ago = time.time() - 600
        for fname in os.listdir('.'):
            if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"):
                st=os.stat(fname)
                mtime=st.st_mtime
                if mtime < ten_minutes_ago:
                    print('removed %s'%fname)
                    os.remove(fname)                    
    
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "dropPhoto":  
        
        # delete the row corresponding to photoID from database and also delete photo from s3
        photoID = jsondata["photoID"]
        toolKaiserID = jsondata["toolKaiserID"]
        print("photoID:= ", photoID)            
        
        # connect to the database to get the filename corresponding to the photoID
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
        # cursor
        cur = con.cursor()
        
        #cur.execute("DELETE from userDataDB WHERE photoid = "+"\'"+photoID+"\'"+" AND "+"toolKaiserid = "+"\'"+toolKaiserID+"\'") 
        # mark the photo as deleted rather than actually deleting it
        cur.execute("UPDATE userDataDB SET status='deleted' WHERE id = "+"\'"+photoID+"\'"+" AND "+"toolKaiserid = "+"\'"+toolKaiserID+"\'") 
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                            
        # response is in json format to be sent back to javascript file
        response = {'comment': "file deleted from database and s3"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)

        # remove any .jpg files which are older than 10 minutes            
        ten_minutes_ago = time.time() - 600
        for fname in os.listdir('.'):
            if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"):
                st=os.stat(fname)
                mtime=st.st_mtime
                if mtime < ten_minutes_ago:
                    print('removed %s'%fname)
                    os.remove(fname)                        
                    
        # delete from S3        
        #delete_s3_file(S3_BUCKET_NAME, 'public/'+photoID+'.jpg')
    
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "saveCanvas":        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]        
    
        # generate a random string for canvasID to be stored in database and upload to S3
        canvasID = ''.join(random.choices(string.ascii_uppercase + string.digits, k=12))        
    
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"] 
        # get canvasString
        canvasString = jsondata["canvasString"]            
        # get toolboxes
        toolboxes = jsondata["toolboxes"] 
        # get jpegData
        jpegData = jsondata["jpegData"] 

        # create JSON data from jpegData and toolboxes
        jpegToolBoxData = {}
        #svgToolBoxData['svgString'] = svgString
        jpegToolBoxData['toolboxes'] = toolboxes
        jpegToolBoxData['jpegData'] = jpegData
        
        #print('jpegData', jpegData)

        # write the the above JSON data to a file with name canvasID.json
        jpegFilename = canvasID+'.json'
        with open(jpegFilename, "w") as f:
            json.dump(jpegToolBoxData,f,indent=4,sort_keys=True)
        
        print('canvasID:-', canvasID)
        
        # write the canvasString to a file with name canvasID.txt
        canvasFilename = canvasID+'.txt'
        text_file = open(canvasFilename, "w")
        text_file.write('%s' % canvasString)
        text_file.close()  

                
        # upload to S3    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
            uploaded = upload_to_aws(canvasFilename, S3_BUCKET_NAME, 'public/'+canvasID+'.txt')
            uploaded = upload_to_aws(jpegFilename, S3_BUCKET_NAME, 'public/'+canvasID+'.json')
            
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':        
            # connect to the database
            
            # for localhost server and local database
            #con = psycopg2.connect(host="localhost", database="mydb", user="postgres", password="Kudekar", port=5432)
            
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            # set the backgroundPaperSize to be NA in this case since we have a cnavas of many tools which might have
            # different backgroundPaperSize
            #cur.execute("insert into userDataDB (toolKaiserid, photoid, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, photoID, fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor))  
            cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, canvasID, 'c', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.jpg which are older than 10 minutes
            ten_minutes_ago = time.time() - 600
            for fname in os.listdir('.'):
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        # response is in json format to be sent back to javascript file
        response = {'comment': "canvas uploaded to s3 and put in database"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "processOrderGivenToolKaiserID":       
        # get photos from the database which match the toolKaiserID for Wix order processing
        print('Inside processOrderGivenToolKaiserID')
        
        # response is in json format to be sent back to javascript file
        toolKaiserID = jsondata["toolKaiserID"]
                
        # connect to the database
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
        # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs starting from startDate till today 
        #cur.execute("SELECT photoid FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+"CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)") 
        cur.execute("SELECT id FROM userDataDB WHERE status=\'active\' AND toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+ "type = \'c+p\'" + "order by herokutimestamp desc limit 1") 
        
        # this is a list of tuples (rownumber, photoID)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                        
        # jsonID is basically the canvasID (in s3 this is stored as .json)
        jsonID = str(rows[0][0])
        # get the filename without extension and version
        jsonIDWithoutVersion = jsonID.split('_v')[0] 
        # get the version number
        canvasVersionNumber = jsonID.split('_v')[1]
                
        # we download the corresponding JPEG JSON file from AWS bucket 
        jsonFilename = jsonIDWithoutVersion +'_JPEG_v' + canvasVersionNumber + '.json'
        print('before first downloaded')
        # check if download was successful -- if not then download PNG file
        if check(S3_BUCKET_NAME, 'public/'+jsonFilename) == True:        
            downloaded = download_from_aws(jsonFilename, S3_BUCKET_NAME, 'public/'+jsonFilename)                    
        else:
            jsonFilename = jsonIDWithoutVersion +'_FOAM_v' + canvasVersionNumber + '.json'
            print('jsonFilename', jsonFilename)
            downloaded = download_from_aws(jsonFilename, S3_BUCKET_NAME, 'public/'+jsonFilename)
            print('second downloaded:=', downloaded)
                    
        # read the JSON file & throw
        with open(jsonFilename) as f:
            jsonData = json.load(f)
        
        # also add the jsonID/canvasID to the jsonData
        jsonData["canvasID"] = jsonID
                
        # use the toolKaiserID to get data from postgres
        response = jsonData
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "saveCanvasOnCloud":        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]            
        # get the canvas
        canvasJSON = jsondata["canvasJSON"]
        # get the filename to be used to store in s3
        officialCanvasName = canvasJSON["officialCanvasName"]
        print("officialCanvasName:= ", officialCanvasName)
        # get the filename without extension
        officialCanvasNameWithoutExtension = officialCanvasName.split('.json')[0]
    
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"]     

        # write the the above JSON data to a file with name officialCanvasName.json        
        with open(officialCanvasName, "w") as f:
            json.dump(canvasJSON,f,indent=4,sort_keys=True)
                            
        # upload to S3    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':            
            uploaded = upload_to_aws(officialCanvasName, S3_BUCKET_NAME, 'public/'+officialCanvasName)
            
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':        
            # connect to the database
            
            # for localhost server and local database
            #con = psycopg2.connect(host="localhost", database="mydb", user="postgres", password="Kudekar", port=5432)
            
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            # delete entries corresponding to the officialCanvasNameWithoutExtension
            cur.execute("DELETE from userDataDB WHERE id = "+"\'"+officialCanvasNameWithoutExtension+"\'"+" AND "+"type = "+"\'c\'") 
            
            # insert the new entry into the database
            cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.jpg which are older than 10 minutes
            ten_minutes_ago = time.time() - 600
            for fname in os.listdir('.'):
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        # response is in json format to be sent back to javascript file
        response = {'comment': "canvas autosaved to s3 and put in database"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "retrieveCanvasFromCloud":        
        
        # get the filename to be used to store in s3
        officialCanvasName = jsondata["officialCanvasName"]
        
        # if officialCanvasName is empty then look at canvasID and toolKaiserID
        if officialCanvasName == "":
            # get the canvasID
            canvasID = jsondata["canvasID"] 
            # find the latest version corresponding to canvasID from database                   
            # connect to the database
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
                
            # cursor
            cur = con.cursor()
            
            # get all the canvases correspoding to canvasID
            cur.execute("SELECT id FROM userDataDB WHERE id like "+"\'"+canvasID+"%\'" + " AND "+"type = "+"\'c\' ORDER BY herokutimestamp DESC")         
            # find the latest version number            
            
            # this is a list of tuples (rownumber, photoID, backgroundpapersize)
            rows = cur.fetchall()
            
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
            
            # close the cursor
            cur.close()
            
            # close the connection
            con.close()
            
            print('rows = ', rows, len(rows))
            if len(rows) > 0:
                officialCanvasID = rows[0][0]
                officialCanvasName = officialCanvasID + '.json'
            print('officialCanvasName from canvasID = ', officialCanvasName)
            if officialCanvasName == "":
                # get toolKaiser ID
                toolKaiserID = jsondata["toolKaiserID"] 
                # get the latest canvases                    
                canvasID = jsondata["canvasID"] 
                # find the latest version corresponding to canvasID from database                   
                # connect to the database
                con = psycopg2.connect(DATABASE_URL, sslmode='require')
                    
                # cursor
                cur = con.cursor()
                
                # get all the canvases correspoding to canvasID
                cur.execute("SELECT id FROM userDataDB WHERE toolkaiserid = "+"\'"+toolKaiserID+"\'" + " AND "+"type = "+"\'c\' ORDER BY herokutimestamp DESC")         
                # find the latest version number            
                
                # this is a list of tuples (rownumber, photoID, backgroundpapersize)
                rows = cur.fetchall()
                
                # commit the transaction otherwise you will not see it in pgadmin
                con.commit()
                
                # close the cursor
                cur.close()
                
                # close the connection
                con.close()
                
                print('rows = ', rows, len(rows))
                if len(rows) > 0:
                    officialCanvasID = rows[0][0]
                    officialCanvasName = officialCanvasID + '.json'
                print('officialCanvasName from toolkaiserID = ', officialCanvasName)
                                         
        print("officialCanvasName = ", officialCanvasName)
        
        # we download the corresponding file from AWS bucket and call processPhoto
        downloaded = download_from_aws(officialCanvasName, S3_BUCKET_NAME, 'public/'+officialCanvasName)        
    
        if downloaded == True:         
            print("download successful")
            # read the canvas JSON file
            with open(officialCanvasName) as f:
                canvasJSON = json.load(f)
        
            # response is in json format to be sent back to javascript file
            response = {'canvasJSON': canvasJSON, 'errorMessage': "success"}
        
            # encode response using jsonpickle
            response_pickled = jsonpickle.encode(response)
        else:
            # response is in json format to be sent back to javascript file
            response = {'canvasJSON': {}, 'errorMessage': "file not found"}
        
            # encode response using jsonpickle
            response_pickled = jsonpickle.encode(response)

        # remove any .jpg or .json files which are older than 10 minutes            
        ten_minutes_ago = time.time() - 600
        for fname in os.listdir('.'):
            if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".JSON") or fname.endswith(".json"):
                st=os.stat(fname)
                mtime=st.st_mtime
                if mtime < ten_minutes_ago:
                    print('removed %s'%fname)
                    os.remove(fname)                    
    
        return Response(response=response_pickled, status=200, mimetype="application/json")
    
    elif task == "saveCanvasAndJPEGOnCloud":        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]               
        
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"] 
        # get canvasString
        canvasJSON = jsondata["canvasJSON"]   
        # get the filename to be used to store in s3
        officialCanvasName = canvasJSON["officialCanvasName"]
        print("officialCanvasName:= ", officialCanvasName)
        # get the filename without extension
        officialCanvasNameWithoutExtension = officialCanvasName.split('.json')[0]  
        # get the filename without extension and version
        officialCanvasNameWithoutExtensionAndVersion = officialCanvasNameWithoutExtension.split('_v')[0] 
        # get the version number
        canvasVersionNumber = officialCanvasNameWithoutExtension.split('_v')[1]
        
        # get toolboxes
        toolboxes = jsondata["toolboxes"] 
        # get jpegData
        jpegData = jsondata["jpegData"] 
        
        # create JSON data from jpegData and toolboxes
        jpegToolBoxData = {}
        #svgToolBoxData['svgString'] = svgString
        jpegToolBoxData['toolboxes'] = toolboxes
        jpegToolBoxData['jpegData'] = jpegData
        
        #print('jpegData', jpegData)
        
        # write the the above JSON data to a file with name canvasID.json
        jpegFilename = officialCanvasNameWithoutExtensionAndVersion +'_JPEG_v' + canvasVersionNumber + '.json'
        with open(jpegFilename, "w") as f:
            json.dump(jpegToolBoxData,f,indent=4,sort_keys=True)
        
        print('officialCanvasNameWithoutExtension:-', officialCanvasNameWithoutExtension)
        
        # write the the above JSON data to a file with name officialCanvasName.json 
        orderCanvasName = officialCanvasNameWithoutExtensionAndVersion +'_ORDER_v' + canvasVersionNumber + '.json'
        # before dumping the JSON data change the officialCanvasName to include _ORDER_
        canvasJSON["officialCanvasName"] = orderCanvasName
        with open(orderCanvasName, "w") as f:
            json.dump(canvasJSON,f,indent=4,sort_keys=True)
        
                
        # upload to S3    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':            
            uploaded = upload_to_aws(orderCanvasName, S3_BUCKET_NAME, 'public/'+orderCanvasName)
            uploaded = upload_to_aws(jpegFilename, S3_BUCKET_NAME, 'public/'+jpegFilename)
            
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':        
            # connect to the database
            
            # for localhost server and local database
            #con = psycopg2.connect(host="localhost", database="mydb", user="postgres", password="Kudekar", port=5432)
            
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            # delete entries corresponding to the officialCanvasNameWithoutExtension
            #cur.execute("DELETE from userDataDB WHERE id = "+"\'"+officialCanvasNameWithoutExtension+"\'"+" AND "+"type = "+"\'c+p\'") 
            
            # insert the new entry into the database
            cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c+p', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.jpg which are older than 10 minutes
            ten_minutes_ago = time.time() - 600
            for fname in os.listdir('.'):
                if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        # response is in json format to be sent back to javascript file
        response = {'comment': "canvas and jpeg saved to s3 and put in database"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "saveCanvasAndFoamOnCloud":  
        
        # "foam" here represents generic data format: svgString, png, gif, jpeg
        
        # get toolKaiser ID
        toolKaiserID = jsondata["toolKaiserID"]               
        
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"] 
        # get canvasString
        canvasJSON = jsondata["canvasJSON"]   
        # get the filename to be used to store in s3
        officialCanvasName = canvasJSON["officialCanvasName"]
        print("officialCanvasName:= ", officialCanvasName)
        # get the filename without extension
        officialCanvasNameWithoutExtension = officialCanvasName.split('.json')[0]  
        # get the filename without extension and version
        officialCanvasNameWithoutExtensionAndVersion = officialCanvasNameWithoutExtension.split('_v')[0] 
        # get the version number
        canvasVersionNumber = officialCanvasNameWithoutExtension.split('_v')[1]
        
        # get toolboxes
        toolboxes = jsondata["toolboxes"] 
        # get foamData
        foamData = jsondata["foamData"] 
        
        # create JSON data from foamData and toolboxes
        foamToolBoxData = {}
        #svgToolBoxData['svgString'] = svgString
        foamToolBoxData['toolboxes'] = toolboxes
        foamToolBoxData['foamData'] = foamData
        
        #print('foamData', foamData)
        
        # write the the above JSON data to a file with name canvasID.json
        foamFilename = officialCanvasNameWithoutExtensionAndVersion +'_FOAM_v' + canvasVersionNumber + '.json'
        with open(foamFilename, "w") as f:
            json.dump(foamToolBoxData,f,indent=4,sort_keys=True)
        
        print('officialCanvasNameWithoutExtension:-', officialCanvasNameWithoutExtension)
        
        # write the the above JSON data to a file with name officialCanvasName.json 
        orderCanvasName = officialCanvasNameWithoutExtensionAndVersion +'_ORDER_v' + canvasVersionNumber + '.json'
        # before dumping the JSON data change the officialCanvasName to include _ORDER_
        canvasJSON["officialCanvasName"] = orderCanvasName
        with open(orderCanvasName, "w") as f:
            json.dump(canvasJSON,f,indent=4,sort_keys=True)
        
                
        # upload to S3    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':            
            uploaded = upload_to_aws(orderCanvasName, S3_BUCKET_NAME, 'public/'+orderCanvasName)
            uploaded = upload_to_aws(foamFilename, S3_BUCKET_NAME, 'public/'+foamFilename)
            
        # write things in the postgres database on Heroku    
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':        
            # connect to the database
            
            # for localhost server and local database
            #con = psycopg2.connect(host="localhost", database="mydb", user="postgres", password="Kudekar", port=5432)
            
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            # delete entries corresponding to the officialCanvasNameWithoutExtension
            #cur.execute("DELETE from userDataDB WHERE id = "+"\'"+officialCanvasNameWithoutExtension+"\'"+" AND "+"type = "+"\'c+p\'") 
            
            # insert the new entry into the database
            cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, officialCanvasNameWithoutExtension, 'c+p', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA"))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.png which are older than 10 minutes
            ten_minutes_ago = time.time() - 600
            for fname in os.listdir('.'):
                if fname.endswith(".png") or fname.endswith(".PNG") or fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
    
        # response is in json format to be sent back to javascript file
        response = {'comment': "canvas and foam saved to s3 and put in database"}
    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        
        return Response(response=response_pickled, status=200, mimetype="application/json")   
    elif task == "processIDAndEmail":       
        # get toolKaiserID and email of the user
        
        # response is in json format to be sent back to javascript file
        toolKaiserID = jsondata["toolKaiserID"]
        # get the user email 
        userEmail = jsondata["userEmail"]    
        # get platform type
        platform = jsondata["platform"]
        # get browser type
        browser = jsondata["browser"]
        # get user timestamp
        userTimeStamp = jsondata["userTimeStamp"]
        
        print('toolKaiserID, userEmail:= ', toolKaiserID, userEmail)
        
        if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':        
            # connect to the database
            
            # for localhost server and local database
            #con = psycopg2.connect(host="localhost", database="mydb", user="postgres", password="Kudekar", port=5432)
            
            con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
            # cursor
            cur = con.cursor()
        
            # insert into database
            now = datetime.now() # current date and time
            herokuTimeStamp = now.strftime("%m/%d/%Y, %H:%M:%S")
            timeOffset = (time.localtime().tm_hour - time.gmtime().tm_hour)*60
            timeOffset += (time.localtime().tm_min - time.gmtime().tm_min)
            timeOffsetHours = math.floor(timeOffset/60)
            if timeOffsetHours < 0:
                timeSign = "-"
            else:
                timeSign = ""
            timeOffsetHours = "%s%02d" % (timeSign,abs(math.floor(timeOffset/60)))
            timeOffsetMinutes = "%02d" % (timeOffset%60)
            timeZone = "GMT"+timeOffsetHours+str(timeOffsetMinutes)
            herokuTimeStamp += ', '+timeZone
            
            # for localhost server and local database
            #cur.execute("insert into myuserdata (filename, browser_type, timestamp, size_of_file) values (%s, %s, %s, %s)", (randomFilename, browser, date_time, 50))  
            
            #cur.execute("insert into userDataDB (toolKaiserid, photoid, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, photoID, fileName, platform, browser, userTimeStamp, herokuTimeStamp, rows, columns, fov, ZoomFactor))  
            cur.execute("insert into userDataDB (toolKaiserid, id, type, filename, platform, browser, usertimestamp, herokutimestamp, rows, columns, fov, zoomfactor, backgroundpapersize, useremail) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", (toolKaiserID, '', 'e', '', platform, browser, userTimeStamp, herokuTimeStamp, 0, 0, 0, 0, "NA", userEmail))  
        
            # commit the transaction otherwise you will not see it in pgadmin
            con.commit()
        
            # close the cursor
            cur.close()
        
            # close the connection
            con.close() 
                
            # delete files *.png which are older than 10 minutes
            ten_minutes_ago = time.time() - 600
            for fname in os.listdir('.'):
                if fname.endswith(".png") or fname.endswith(".PNG") or fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG") or fname.endswith(".txt") or fname.endswith(".TXT") or fname.endswith(".json") or fname.endswith(".JSON"):
                    st=os.stat(fname)
                    mtime=st.st_mtime
                    if mtime < ten_minutes_ago:
                        print('removed %s'%fname)
                        os.remove(fname)
        
                                 
        # response is in json format to be sent back to javascript file
        response = {'comment': "heroku got user email and toolKaiserID"}    
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
        return Response(response=response_pickled, status=200, mimetype="application/json")
    elif task == "changeToolboxNumber":
        
        # generate a random string for filename to be stored in database and upload to S3
        photoID = jsondata["photoID"]
        toolboxNumber = jsondata["toolboxNumber"]
        print("photoID:= ", photoID) 
        print("toolboxNumber:= ", toolboxNumber) 
        
        ### connect to the database and pull the backgroundPaperSize associated to the photoID
        
        # connect to the database
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
            
        # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs starting from startDate till today 
        #cur.execute("SELECT photoid FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'"+" AND "+"CAST(usertimestamp as date) >= CAST(\'"+startDate+"\' as date)") 
        cur.execute("UPDATE userDataDB SET toolboxnumber="+ str(toolboxNumber) + " WHERE id = "+"\'"+photoID+"\'")                 
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
          
        # response is in json format to be sent back to javascript file
        response = {'comment': "photo moved to " + str(toolboxNumber)}     
        
        # encode response using jsonpickle
        response_pickled = jsonpickle.encode(response)
    
        return Response(response=response_pickled, status=200, mimetype="application/json")        
    else:
        print("No task provided!")
        

@app.route('/')
def index():
    return render_template('index.html')

# start flask app
if __name__ == '__main__':
    
    if imgProcFunctions_v3.MYENVIRONMENT == 'HEROKU':
        #app.use(express.static(path.join(__dirname, 'app/templates')))
        app.run()   
    else:
        print('python version:= ', sys.version)
        app.run(host="127.0.0.1", port=5000, debug=True)
