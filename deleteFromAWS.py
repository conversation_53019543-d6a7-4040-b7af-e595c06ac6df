#postgres library
import psycopg2
import datetime
from datetime import date
import string
import random
import os, boto3
from botocore.exceptions import NoCredentialsError
import time
import sys

# Amazon S3 access    
ACCESS_KEY = '********************'
SECRET_KEY = '4/EcoiSTPxQASMv0ssJJT9b96mnVSaPvueCpybBJ'
S3_BUCKET_NAME = 'toolkaiser'
toolKaiserID = "alkjakljf" # if this is empty "", then all the images in public/ are downloaded to dropbox
FOLDER = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//AWS_Images'
past = 1 # get data X days back
DATABASE_URL = "postgres://xwjlcqdzbdcffx:<EMAIL>:5432/dcejk9q4ujbmld"  
  
# function to download file from S3 and save to local_file
def download_from_aws(local_file, bucket, s3_file):
    s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    
    try:
        s3.download_file(bucket, s3_file, local_file)
        print("Download Successful")
        return True
    except FileNotFoundError:
        print("The file was not found")
        return False
    except NoCredentialsError:
        print("Credentials not available")
        return False

# function to delete a file from S3
def delete_s3_file(bucket, s3_file):    
    s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    s3.delete_object(Bucket=bucket, Key=s3_file)
    print("Deleting %s" % s3_file)    


if __name__ == '__main__':
    
    if toolKaiserID == "":        
        print("Please provide a toolKaiserID!")
    else:
        
        # connect to the local database
        #con = psycopg2.connect(host="localhost", database="myLocalUserDataDB", user="postgres", password="Kudekar", port=5432)
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
         # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs with the given toolkaiser id 
        cur.execute("SELECT id FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'") 
        
        # this is a list of tuples (rownumber, photoID)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                  
        #photoIDs = []
        for r in rows:
            photoID = r[0]
            full_filename = os.path.join(FOLDER,photoID+'.jpg')
            print('deleting file = ', full_filename)
            # delete from S3        
            delete_s3_file(S3_BUCKET_NAME, 'public/'+photoID+'.jpg')
