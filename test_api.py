#!/usr/bin/env python3
"""
Simple test script to verify the ToolKaiser API is working
"""

import requests
import json
import base64
from PIL import Image
import io

def create_test_image():
    """Create a simple test image"""
    # Create a simple white image with some text
    img = Image.new('RGB', (400, 300), color='white')
    
    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    return img_base64

def test_api():
    """Test the ToolKaiser API"""
    url = "http://127.0.0.1:5000/api/sendImg"
    
    # Create test data
    test_data = {
        "task": "processPhoto",
        "img": create_test_image(),
        "fileName": "test.jpg",
        "platform": "web",
        "browser": "chrome",
        "userTimeStamp": "2024-01-01",
        "backgroundPaperSize": "A4",
        "FOV": 57.72,
        "ZoomFactor": 1.0,
        "toolKaiserID": "test123",
        "appVersionNumber": "1.2.0"  # This was missing in the previous test
    }
    
    try:
        print("🧪 Testing ToolKaiser API...")
        print(f"📡 Sending POST request to: {url}")
        
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API is working successfully!")
            result = response.json()
            print(f"📝 Response keys: {list(result.keys())}")
            if 'numOfTools' in result:
                print(f"🔧 Number of tools detected: {result['numOfTools']}")
        else:
            print(f"❌ API returned error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure it's running on http://127.0.0.1:5000/")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api()
