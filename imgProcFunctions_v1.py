# -*- coding: utf-8 -*-
"""
Created on Mon Mar  2 13:23:04 2020

@author: SK & RU
"""

import numpy as np
import cv2
import imutils
import os
import matplotlib.pyplot as plt
import itertools

MYENVIRONMENT = 'HEROKU' # for pushing onto herokukaiser and deploying on heroku
MYENVIRONMENT = 'PYTHON' # delete for heroku # set LOCAL to run things on locolhost; set PYTH<PERSON> to run things with dropbox data
TESTING = False # testing for .txt output with image size; background paper size; offsetx; offsety; convex hull data; raw path data
DEBUG = True # for detail debugging of one example with pictures
SIGMA = 0.33 # for Canny edges detection
THRESH_CONTOUR = 50 # for removing really small contours
USE_SEGMENTATION = True # segmentation or edge detection
USE_SEGMENTATION_TOOL_OUTLINE = False # use segmentation also for tool outline detection
USE_ORIGINAL_IMAGE = True # use the image before perspectvive transformation for tool edge detection

# size of A4 paper in mm
A4_h = 297
A4_w = 210

# perimeter length of the contour
def cntrperi(cnt):
   return cv2.arcLength(cnt,True)

# area of the contour
def cntrarea(cnt):
    return cv2.contourArea(cnt)

# get the area of a rectangular box
def get_area(box):
    h = np.linalg.norm(box[1]-box[2])
    w = np.linalg.norm(box[2]-box[3])
    
    return h*w

# Finds the intersection of two lines given in Hesse normal form. Returns closest integer pixel locations.
# See https://stackoverflow.com/a/383527/5087436
def intersection(line1, line2):

    rho1 = line1[0]; theta1 = line1[1]
    rho2 = line2[0]; theta2 = line2[1]
    A = np.array([
        [np.cos(theta1), np.sin(theta1)],
        [np.cos(theta2), np.sin(theta2)]
    ])        
    b = np.array([[rho1], [rho2]])
    #print(A,b,np.linalg.det(A))
    x0, y0 = np.linalg.solve(A, b)
    x0, y0 = int(np.round(x0)), int(np.round(y0))
    return np.array([x0, y0],dtype=np.int32)


def plot_lines(image, pruned_lines):
    #Display on the original image
    sh = image.shape
    for i in range(0,len(pruned_lines)):
       rho = pruned_lines[i][0]
       theta = pruned_lines[i][1]
       a = np.cos(theta)
       b = np.sin(theta)
         
       x0 = a*rho
       y0 = b*rho
       x1 = int(x0 + np.maximum(sh[0],sh[1])*(-b))
       y1 = int(y0 + np.maximum(sh[0],sh[1])*(a))
       x2 = int(x0 - np.maximum(sh[0],sh[1])*(-b))
       y2 = int(y0 - np.maximum(sh[0],sh[1])*(a))
       # overlay line on original image
       cv2.line(image,(x1,y1),(x2,y2),(0,255,0),2)

# check if line l is "contained" in the set of lines
def is_contained_in_lines_seg(myl, mylines, sh_img, TOLERANCE):
    
    if len(mylines) == 0:
        return False    
    
    # first make sure things are ok when rho is negative
    if myl[0] < 0:        
        myl[1] = myl[1] - np.pi             
        myl[0] = np.abs(myl[0])
            
    for i in range(0,len(mylines)):
        if mylines[i][0] < 0:
            mylines[i][0] = np.abs(mylines[i][0])
            mylines[i][1] = mylines[i][1] - np.pi
            
    temp_lines = np.abs(mylines - myl)    
    if (np.min(np.abs(temp_lines[:,0]/np.maximum(sh_img[0],sh_img[1])) + np.abs(temp_lines[:,1]/np.pi))) <= TOLERANCE:
        return True
    else:
        return False

# perscpective transformation 
def get_perspective_transform(bkgrnd_box, sh_src, sh_dst, img, gray):                  
    #pts2 = np.float32([[0,0],[sh_img[1],0],[sh_img[1],sh_img[0]],[0,sh_img[0]]])
    #pts2 = np.float32([[0,0],[int(scale*A4_w),0],[int(scale*A4_w),int(scale*A4_h)],[0,int(scale*A4_h)]])
    
    pts2 = np.float32([[0,0],[sh_src[1],0],[sh_src[1],sh_src[0]],[0,sh_src[0]]])
    pts1 = -1*np.ones(np.shape(pts2))
    for i in range(0,4):
        d = 100000
        for j in range(0,len(bkgrnd_box[0])):        
            if np.linalg.norm(pts2[i]-bkgrnd_box[0][j]) <= d:
                d = np.linalg.norm(pts2[i]-bkgrnd_box[0][j])
                pts1[i,:] = bkgrnd_box[0][j]
                
    pts1 = np.float32(pts1)
    pts2 = np.float32([[0,0],[sh_dst[1],0],[sh_dst[1],sh_dst[0]],[0,sh_dst[0]]])
    # rect height and width
    #width = np.linalg.norm(pts1[0]-pts1[1])
    #height = np.linalg.norm(pts1[1]-pts1[2])
    M = cv2.getPerspectiveTransform(pts1,pts2)
    #gray_new = cv2.warpPerspective(gray,M,(sh_img[1],sh_img[0]))
    #img_new = cv2.warpPerspective(img,M,(sh_img[1],sh_img[0]))
    #gray_new = cv2.warpPerspective(gray,M,(int(scale*A4_w),int(scale*A4_h)))
    #img_new = cv2.warpPerspective(img,M,(int(scale*A4_w),int(scale*A4_h)))
    gray_new = cv2.warpPerspective(gray,M,(sh_dst[1],sh_dst[0]))
    img_new = cv2.warpPerspective(img,M,(sh_dst[1],sh_dst[0]))
    #closing_new = cv2.warpPerspective(img_closing,M,(sh_dst[1],sh_dst[0]))
    
    return img_new, gray_new


# for images where object has a little part touching the background paper 
# we cleanup the boundary by setting a small sliver at each side to 0
def clean_up_boundary(im):
    # just make the pixels at the boundary black
    THRESH_BNDRY = 5 # this changes to 9 for IMG_5015
    im[0:THRESH_BNDRY, :] = 0
    im[im.shape[0]-THRESH_BNDRY:im.shape[0], :] = 0
    im[:,0:THRESH_BNDRY] = 0
    im[:,im.shape[1]-THRESH_BNDRY:im.shape[1]] = 0
    
    return im

# given box1 and box2, check if one is inside the other
def check_box_within_box(box1,box2):
    # output: 1 if box2 is within box1; 2 if box1 is within box2; 0 else
    TOLERANCE_DIST = 40

    area1 = get_area(box1); area2 = get_area(box2)
    h1 = np.linalg.norm(box1[1]-box1[2])
    w1 = np.linalg.norm(box1[2]-box1[3])
    h2 = np.linalg.norm(box2[1]-box2[2])
    w2 = np.linalg.norm(box2[2]-box2[3])
    flag = 0
    if area1 >= area2:
        l1 = box1[0] - box1[1];  l2 = box1[1] - box1[2]; l3 = box1[2] - box1[3]; l4 = box1[3] - box1[0];
        for j in range(0,len(box2)):
            d1 = np.cross(l1,box1[0]-box2[j])/np.linalg.norm(l1);
            d2 = np.cross(l3,box1[2]-box2[j])/np.linalg.norm(l3);
            d3 = np.cross(l2,box1[1]-box2[j])/np.linalg.norm(l2);
            d4 = np.cross(l4,box1[3]-box2[j])/np.linalg.norm(l4);
            da = np.abs(d1)+np.abs(d2); db = np.abs(d3)+np.abs(d4)
            if (np.abs(da - h1) <= TOLERANCE_DIST) and (np.abs(db - w1) <= TOLERANCE_DIST):
                flag = 1
            else:
                flag = 0
                break
    else:
        l1 = box2[0] - box2[1];  l2 = box2[1] - box2[2]; l3 = box2[2] - box2[3]; l4 = box2[3] - box2[0];
        for j in range(0,len(box1)):
            d1 = np.cross(l1,box2[0]-box1[j])/np.linalg.norm(l1);
            d2 = np.cross(l3,box2[2]-box1[j])/np.linalg.norm(l3);
            d3 = np.cross(l2,box2[1]-box1[j])/np.linalg.norm(l2);
            d4 = np.cross(l4,box2[3]-box1[j])/np.linalg.norm(l4);
            da = np.abs(d1)+np.abs(d2); db = np.abs(d3)+np.abs(d4)
            if (np.abs(da - h2) <= TOLERANCE_DIST) and (np.abs(db - w2) <= TOLERANCE_DIST):
                flag = 2   
            else:
                flag = 0
                break
       
    return flag

# remove boxes within a box
def remove_inner_boxes(rect_box, good_box, all_hulls, candidate_cntrs, img_copy):
    pruned_box = [] # remove boxes which are within a box and prune the original list of boxes
    pruned_hulls = []
    pruned_cntrs = []
    # consider every pair of boxes and check if one box is within the other
    for i in range(0,len(rect_box)):
        if good_box[i] == True:
            for j in range(0,len(rect_box)):
                if i != j:
                    flag = check_box_within_box(rect_box[i],rect_box[j])
                    if flag == 1: # j is within i
                        good_box[j] = False
                    if flag == 2: # i is within j
                        good_box[i] = False
                        break       
    for i in range(0,len(rect_box)):
        if good_box[i] == True:                
            pruned_box.append(rect_box[i])
            pruned_hulls.append(all_hulls[i])
            pruned_cntrs.append(candidate_cntrs[i])
            cv2.drawContours(img_copy,[rect_box[i]],0,(0,255,0),4)
            cv2.drawContours(img_copy, [all_hulls[i]], -1, (0,0,255), 4) 
    return pruned_box, pruned_hulls, pruned_cntrs


# check closeness of boxes of max_box and box
def check_closeness(max_box, box, sh):
    
    # tolerance to determine closeness
    TOLERANCE_CLOSE = np.min([25,sh[0]/20,sh[1]/20]) 

    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print(box)        
    flag = False
    
    # if box subsumed by max_box then don't merge
    # pointPolygonTest gives the shortest signed distance between a point and a contour
    # if distance is negative it is outside, if positive then inside, if 0 then on the contour
    a= cv2.pointPolygonTest(np.asarray(max_box),tuple(box[0].tolist()),True)
    b= cv2.pointPolygonTest(np.asarray(max_box),tuple(box[1].tolist()),True)
    c= cv2.pointPolygonTest(np.asarray(max_box),tuple(box[2].tolist()),True)
    d= cv2.pointPolygonTest(np.asarray(max_box),tuple(box[3].tolist()),True)
    # box inside max_box
    if(a >= 0 and b >= 0 and c >= 0 and d >= 0): 
        flag = False
        return flag
    
    # at least one corner of box is inside max_box
    if(a >= 0 or b >= 0 or c >= 0 or d >= 0): 
        flag = True    
    # all corners are outside but at least one corner of box is close to max_box
    elif (-1.0*a <= TOLERANCE_CLOSE or -1.0*b <= TOLERANCE_CLOSE or -1.0*c <= TOLERANCE_CLOSE or -1.0*d <= TOLERANCE_CLOSE):
        flag = True      
    # at this point we have that all four corners of box are outside max_box
    # and they are quite far away. but we could have that at least one corner of 
    # max_box is close to box
    else:
        u= cv2.pointPolygonTest(np.asarray(box),tuple(max_box[0].tolist()),True)
        v= cv2.pointPolygonTest(np.asarray(box),tuple(max_box[1].tolist()),True)
        w= cv2.pointPolygonTest(np.asarray(box),tuple(max_box[2].tolist()),True)
        x= cv2.pointPolygonTest(np.asarray(box),tuple(max_box[3].tolist()),True)
        if (-1.0*u <= TOLERANCE_CLOSE or -1.0*v <= TOLERANCE_CLOSE or -1.0*w <= TOLERANCE_CLOSE or -1.0*x <= TOLERANCE_CLOSE):
            flag = True

    return flag

# check if any other contour can be merged with the max_cntr
def merge_cntrs(max_cntr, other_cntrs, sh):
    
    # find box/rect around the max_cntr call it max_box
    rect = cv2.minAreaRect(max_cntr)            
    max_box = cv2.boxPoints(rect)
    max_box = np.int0(max_box)
    # get the height and the width of the box around max_cntr
    h1 = np.linalg.norm(max_box[1]-max_box[2])
    w1 = np.linalg.norm(max_box[2]-max_box[3])
    # vector representation for each line of the max_box
    l1 = max_box[0] - max_box[1];  l2 = max_box[1] - max_box[2]; l3 = max_box[2] - max_box[3]; l4 = max_box[3] - max_box[0];        
    
    # go over the other_cntrs and check if any rect/box around that cntr is close to the max_cntr
    # if "close" then merge it with the max_cntr
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('other_cntrs',len(other_cntrs))
    for c in other_cntrs:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('candidate cntr for merging: ', len(c))
        rect = cv2.minAreaRect(c)            
        box = cv2.boxPoints(rect)
        box = np.int0(box)   
        # get the coordinates of the center of the box
        center_x = (box[0][0]+box[1][0]+box[2][0]+box[3][0])/4
        center_y = (box[0][1]+box[1][1]+box[2][1]+box[3][1])/4
        # make it a numpy array
        center = np.asarray([center_x, center_y], dtype = np.int64)        
        # get total distance of the center of box from the first pair of opposite lines of max_box
        d1 = np.cross(l1,max_box[0]-center)/np.linalg.norm(l1)
        d2 = np.cross(l3,max_box[2]-center)/np.linalg.norm(l3)
        da = np.abs(d1)+np.abs(d2) 
        # get total distance of the center of box from the other pair of opposite lines of max_box
        d3 = np.cross(l2,max_box[1]-center)/np.linalg.norm(l2)
        d4 = np.cross(l4,max_box[3]-center)/np.linalg.norm(l4)
        db = np.abs(d3)+np.abs(d4)        
        if (da - h1) > 0 or (db - w1)  > 0: # box is "outside"; candidate for merging            
            # check if one corner of the box is close to any one of the corners of the max_box            
            if (check_closeness(max_box, box, sh)):
                # boxes are close and we now merge them
                max_cntr = np.concatenate((max_cntr,c),axis=0)                
    return max_cntr

# find_contours followed by pruning the contours
def get_pruned_cntrs(image,image_orig):
    
    pruned_cntrs = []
    img_copy = np.copy(image_orig)    
    # get contours around the objects
    major = cv2.__version__.split('.')[0]
    if major == '3':
        im2, cnts, hier = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        cnts, hier = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrperi, reverse = True)[:20] #sort the top 20
    
    max_cntr = []
    other_cntrs = [] # list of all cntrs which are not the max_cntr
    max_area = 0
    for c in cnts:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON': 
            rect = cv2.minAreaRect(c)            
            box = cv2.boxPoints(rect)
            box = np.int0(box) 
            cv2.drawContours(img_copy, [c], 0, (0,255,0), 4) 
            #print(cntrperi(c), cntrarea(c), box)
            #cv2.drawContours(img_copy, [box], 0, (0,0,255), 4)
    	# if the contour has really small perimeter, discard it
        if cntrperi(c) > THRESH_CONTOUR:                       
            rect = cv2.minAreaRect(c)            
            box = cv2.boxPoints(rect)
            box = np.int0(box)                  
            area = cntrarea(c)
            #if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            #    print('area', area, 'first_pt', c[0], 'last_pt', c[len(c)-1], 'box', box)
            if (area >= max_area):                
                # found a contour of larger size
                # put the previous max_cntr, if not empty, into other_cntrs
                if (len(max_cntr)>0):
                    other_cntrs.append(max_cntr)
                max_area = area
                max_cntr = c
            else:
                if (len(max_cntr)>0):
                    other_cntrs.append(c)
                
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':        
        print('number of other cntrs: ',len(other_cntrs))
        for c in other_cntrs:
            print('first point in cntr: ', c[0])
        print('max_cntr ',len(np.squeeze(max_cntr)))  
        print('first point in max_cntr: ', max_cntr[0])
        plt.figure();plt.imshow(img_copy, cmap='gray')
#    # prune things, i.e., remove contours-within-contours/boxes-within-boxes      
#    pruned_box, pruned_hulls, pruned_cntrs = remove_inner_boxes(rect_box, good_box, all_hulls, candidate_cntrs, image_orig)
    
#    return pruned_box, pruned_cntrs, pruned_hulls
        
    # check if any from the other_cntrs can be merged with the max_cntr
    if (len(other_cntrs) > 0):
        max_cntr = merge_cntrs(max_cntr, other_cntrs, image_orig.shape)
    
    pruned_cntrs.append(max_cntr)
    return pruned_cntrs


# check if intersection of two lines lies outside the image
def intersectWithTwoLines(myline, mylistoflines, sh):    
    count_intersection = 0
    for line in mylistoflines:    
        # ignore lines which have theta very close to each other
        if (np.abs(myline[1]-line[1])) > 1e-4: 
            # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
            if (np.abs(1/np.tan(myline[1])-1/np.tan(line[1]))) > 1e-4:                 
                a = intersection(myline, line)                
                # count the number of intersections inside the image
                if (a[0] < sh[1] and a[0] > 0 and a[1] > 0 and a[1] < sh[0]):            
                    count_intersection += 1        
        
    return count_intersection
            

# get the largest contour in terms of area enclosed
def get_largest_cntr(pruned_cntrs):
        
    max_area = cntrarea(pruned_cntrs[0])
    if len(pruned_cntrs) == 1:
        return pruned_cntrs
    else:
        for c in pruned_cntrs:
            if cntrarea(c) >= max_area:
                largest_cntr = c
                max_area = cntrarea(c)
                #print(max_area)
    return [largest_cntr]

# get Hough lines from the edge detection image
def get_lines_edge_image(gray, blur_size, hough_threshold, sh_img):
    # Gaussian smoothing to remove spurious edges
    blur = cv2.GaussianBlur(gray,(blur_size,blur_size),0) 
    # compute the median of the single channel pixel intensities
    v = np.median(blur)
    # apply automatic Canny edge detection using the computed median
    # use SIGMA = 0.33 here 
    lower = int(max(0, (1.0 - SIGMA) * v))
    upper = int(min(255, (1.0 + SIGMA) * v))
    #lower = 100
    #upper = 200
    edges = cv2.Canny(blur,lower,upper)    
    # "closing" transformation        
    # kernel for "closing" transformation 
    kernel_close = np.ones((7,7),np.uint8)
    img_for_bkgrnd_edge = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)
    
    image = img_for_bkgrnd_edge
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('blur_size %d hough_threshold %d lower %d, upper %d for bkgrnd edge detection' % (blur_size, hough_threshold, lower, upper))
        plt.figure();plt.imshow(img_for_bkgrnd_edge, cmap='gray')
    
    # get the Hough lines on the edge image
    lines_edge = cv2.HoughLines(image,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/hough_threshold))    
    lines_edge = np.squeeze(lines_edge)
    
    return lines_edge

# check if there are 4 distinct lines that we want or not (this is mostly when we have around <50 lines)
def get_num_distinct_lines(lines_edge):
    
    num = len(lines_edge) # starting number of distinct lines
    distinct_lines = np.ones(num,dtype=int) # all are distinct at the start
    #print(distinct_lines)
    for i in range(0,num):        
        if lines_edge[i][0] < 0:            
            thetai = np.pi - lines_edge[i][1]
        else:
            thetai = lines_edge[i][1]
        for j in range(i+1,num):
            if distinct_lines[j] == 1:
                flag_slope = False
                if lines_edge[j][0] < 0:            
                    thetaj = np.pi - lines_edge[j][1]
                else:
                    thetaj = lines_edge[j][1]
                # sometimes convention of only positive theta is not followed hence we have second condition below
                if np.abs(thetai-thetaj) < 1e-2 or np.abs(1/np.tan(thetai)-1/np.tan(thetaj)) < 1e-2: 
                    flag_slope = True  
                #print(i,j,np.abs(np.abs(lines_edge[i][0]) - np.abs(lines_edge[j][0])),flag_slope)                                  
                if (np.abs(np.abs(lines_edge[i][0]) - np.abs(lines_edge[j][0])) < 5 and flag_slope == True):
                    distinct_lines[j] = 0
    #print(distinct_lines, len(np.nonzero(distinct_lines)[0]))
    return len(np.nonzero(distinct_lines)[0])
    

# get the outline along the tool/object
def get_contours(im):
        
    ##############################################################################################
    ###################  Get the image and convert to appropriate size ###########################
    ##############################################################################################
    
    a = im.shape[0]
    b = im.shape[1]
    
    # if image in landscape convert to portrait
    if a <= b:    
        im = imutils.rotate_bound(im, 90)    
    
    # resize image to 640 rows for faster processing; low resolution also makes several CV algos perform better
    a_new = 640    
    img = cv2.resize(im,(int(a_new*im.shape[1]/im.shape[0]), a_new))    
    
    #img = cv2.resize(im,(a_new,int(a_new*b/a)))    
    sh_img = img.shape
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('original size:',im.shape[0],im.shape[1])
        print('size after resizing to 640 rows: ', sh_img[0], sh_img[1])
    
    # get the grayscale image
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    
    ##############################################################################################
    ###################  Get the background paper first ##########################################
    ##############################################################################################
    
    # two ways to get the background paper; currently using the segmentation/thresholding approach
    if USE_SEGMENTATION:
        # simple thresholding for segmentation        
        ret,th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        #img_seg = cv2.bitwise_not(th)        
        # "opening" transformation
        # kernel for "opening" transformation 
        #if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        #    plt.figure();plt.imshow(th, cmap='gray')
        kernel_open = np.ones((7,7),np.uint8)
        img_for_bkgrnd = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            plt.figure();plt.imshow(img_for_bkgrnd, cmap='gray')
        #opening = cv2.morphologyEx(closing, cv2.MORPH_OPEN, kernel_open)
    else:
        # gaussian blur to remove spurious edges
        blur = cv2.GaussianBlur(gray,(3,3),0) # originally 3 was used
        # compute the median of the single channel pixel intensities
        v = np.median(blur)
        # apply automatic Canny edge detection using the computed median
        lower = int(max(0, (1.0 - SIGMA) * v))
        upper = int(min(255, (1.0 + SIGMA) * v))
        #lower = 100
        #upper = 200
        edges = cv2.Canny(blur,lower,upper)    
        # "closing" transformation        
        # kernel for "closing" transformation 
        kernel_close = np.ones((7,7),np.uint8)
        img_for_bkgrnd = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close) 
        
    image = img_for_bkgrnd
    
    # the paper might not be parallel to the axis of the image 
    # use findContours to get the background paper 
    major = cv2.__version__.split('.')[0]
    if major == '3':       
        im2, cnts, hier = cv2.findContours(image,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    else:
        cnts, hier = cv2.findContours(image,  cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = sorted(cnts, key = cntrperi, reverse = True)[:20]
    
    # blank image to draw the big contour which most likely represents the background paper
    img_bl =  np.zeros((sh_img[0],sh_img[1]),dtype = "uint8")
    bkgrnd_box = []
    max_area = 0    
    for c in cnts:
    	# if the contour has really small perimeter, discard it
        if cntrperi(c) > THRESH_CONTOUR:
            rect = cv2.minAreaRect(c)
            box = cv2.boxPoints(rect)
            box = np.int0(box)    
            # get the contour area
            area = cntrarea(c)
            if (area >= max_area):
                max_area = area
                max_cntr = c
                
    # get the temporary bkgrnd_box and draw the contour on a blank image for further processing
    epsilon = 0.075*cv2.arcLength(max_cntr,True)
    approx = cv2.approxPolyDP(max_cntr,epsilon,True)
    rect = cv2.minAreaRect(approx)
    box = cv2.boxPoints(rect)
    box = np.int0(box) 
                    
    bkgrnd_box.append(box) # this bkgrnd_box is used in case of the edge detection process gives < 4 lines
    cv2.drawContours(img_bl,[max_cntr],0,(255,255,255),4)
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure();plt.imshow(img_bl, cmap='gray')
    
    # use Hough lines on the binary image with only the contour
    lines_seg = cv2.HoughLines(img_bl,1,np.pi/360,threshold = int(np.minimum(sh_img[0],sh_img[1])/3.0))    
    lines_seg = np.squeeze(lines_seg)
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        img_copy = np.copy(img)
        plot_lines(img_copy,lines_seg)
        plt.figure();plt.imshow(img_copy, cmap='gray')        
        img_copy = np.copy(img)
        cv2.drawContours(img_copy, bkgrnd_box, -1, (255,0,255), 2)
        plt.figure();plt.imshow(img_copy, cmap='gray')
        
    # bkgrnd box from segmentation process for backup
    bkgrnd_box_seg = np.copy(bkgrnd_box)
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('bkgrnd_box_seg (backup): ', bkgrnd_box_seg)
    
    ##############################################################################################
    ################### Get Hough lines from the Edge detection process ##########################
    ##############################################################################################
    
    # get the Hough lines for the edge detected image
    blur_size = [1,3,5,7]
    hough_threshold = [4,5,6]
    # use blur_size = 3 and hough_threshold = 4 to start with
    lines_edge = get_lines_edge_image(gray, blur_size[1], hough_threshold[0], sh_img)
    # sometimes even if we get > 4 lines, they need not be the ones around the paper;
    # it could just be clustering of multiple lines aroudn one line
    # in this case, we need to check first that we have existence of 4 lines around paper
    # if not then we need to reduce blur and/or decrease hough_threshold
    distinct_lines = len(lines_edge)
    if distinct_lines > 4 and distinct_lines < 50: # if num of lines > 50, then perhaps we assume that we have those lines we want
        distinct_lines = get_num_distinct_lines(lines_edge)
    
    # sometimes background color is close to white paper, then we need to decrease blur to 1 
    # and reduce Hough threshold to sh_img[1]/5 to make sure we get our desired lines  
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        #print(lines_edge)
        print('distinct lines %d' % distinct_lines)
    if distinct_lines < 4:
        # make blur_size = 1        
        for j in range(0,len(hough_threshold)):
            lines_edge = get_lines_edge_image(gray, blur_size[0], hough_threshold[j], sh_img)
            distinct_lines = len(lines_edge)
            if distinct_lines > 4 and distinct_lines < 50: # if num of lines > 50, then perhaps we assume that we have those lines we want
                distinct_lines = get_num_distinct_lines(lines_edge)
            if distinct_lines >= 4:
                break

    # sometimes because of carpet or hardwood or tiles we might get thousands of undesired lines in the edge detection
    # because blurring isn't too large. In such cases when num of lines_edge is > 1000 
    # increase the blurring to first 5, if it is still large > 1000, increase it to 7. 
    # we keep the hough_threshold fixed to 4 so that we are being strict with the lines we want
    elif distinct_lines >= 1000:        
        for i in range(2,len(blur_size)):
            lines_edge = get_lines_edge_image(gray, blur_size[i], hough_threshold[0], sh_img)
            if len(lines_edge) < 1000:
                break            
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        img_copy = np.copy(img)
        #print(lines_edge)
        plot_lines(img_copy,lines_edge)
        print('number of lines_edge: %d' % len(lines_edge))
        plt.figure();plt.imshow(img_copy, cmap='gray')    
        #print('lines from segmentation')
        #print(lines_seg)        
        #print('lines from edge detection')
        #print(lines_edge)
    
    # get the intersection of Hough lines obtained by the two methods above: lines_seg and lines_edge
    line_count = 0
    lines = []
    for l in lines_edge:
        # check if we have a new line_edge        
        if is_contained_in_lines_seg(l, lines_seg, sh_img, 0.02) and not (is_contained_in_lines_seg(l, lines, sh_img, 0.1)): 
            lines.append(l)
            line_count += 1
        
    # convert to list of numbers
    if (len(lines) > 0):
        lines = np.asarray(lines,dtype=np.float32)
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('intersected lines')
        print(lines)
        img_copy = np.copy(img)
        plot_lines(img_copy,lines)
        plt.figure();plt.imshow(img_copy, cmap='gray')
        
    if line_count < 4:
        print('Some sing is wong!!! Less than four lines (%d) found!!! Using the bkgrnd_box found in segmentation!' % line_count)
        bkgrnd_box = bkgrnd_box_seg
    elif line_count > 4:
        bkgrnd_box = []
        # check if we can refine to get the four lines of the background paper        
        # get the list of indices for lines
        lines_ind = list(range(0,len(lines)))
        # now go through all the 4 combinations
        candidate_rects = []
        candidate_areas = []
        for subset in itertools.combinations(lines_ind, 4):
            flag = True
            #print(subset)
            # set the flag for the "true" four lines to be True at the start
            # get the four lines
            line1 = lines[subset[0]]; line2 = lines[subset[1]]; line3 = lines[subset[2]]; line4 = lines[subset[3]]
            # check if each line intersects with only two other lines inside the image
            a = intersectWithTwoLines(line1, [line2, line3, line4], sh_img)
            b = intersectWithTwoLines(line2, [line1, line3, line4], sh_img)
            c = intersectWithTwoLines(line3, [line1, line2, line4], sh_img)
            d = intersectWithTwoLines(line4, [line1, line2, line3], sh_img)
            #print(a,b,c,d)
            if a!=2 or b!=2 or c!=2 or d!=2: # check if there are two intersection points inside the image
                flag = False                
            # if lines are valid, then compute the area
            if(flag == True):
                # we have perhaps a valid set of four lines
                # find the points of intersection and compute the area
                temp_rect = []
                for i in range(0,len(subset)-1):         
                    for j in range(i+1,len(subset)):
                        line1 = lines[subset[i]]; line2 = lines[subset[j]] 
                        # make sure they aren't parallel lines
                        if (np.abs(line1[1]-line2[1])) > 1e-4: 
                            # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
                            if (np.abs(1/np.tan(line1[1])-1/np.tan(line2[1]))) > 1e-4: 
                                a = intersection(line1, line2)    
                                if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):                         
                                    temp_rect.append(a)
                temp_rect = np.asarray(temp_rect,dtype=np.int64)
                tr = []; tr.append(temp_rect); temp_rect = tr
                # order the corners in bkgrnd_box
                if (np.linalg.norm(temp_rect[0][0] - temp_rect[0][1]) >= np.linalg.norm(temp_rect[0][0] - temp_rect[0][2])):
                    temp_pt = np.copy(temp_rect[0][1])
                    temp_rect[0][1] = temp_rect[0][2]
                    temp_rect[0][2] = temp_pt
                if (np.linalg.norm(temp_rect[0][1] - temp_rect[0][2]) >= np.linalg.norm(temp_rect[0][1] - temp_rect[0][3])):
                    temp_pt = np.copy(temp_rect[0][2])
                    temp_rect[0][2] = temp_rect[0][3]
                    temp_rect[0][3] = temp_pt
                # take the area of the candidate rectangle
                area1 = np.abs(np.cross(temp_rect[0][0] - temp_rect[0][1], temp_rect[0][0] - temp_rect[0][3]))/2.0 
                area2 = np.abs(np.cross(temp_rect[0][2] - temp_rect[0][1], temp_rect[0][2] - temp_rect[0][3]))/2.0 
                if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                    print(subset, area1+area2, temp_rect)
                candidate_rects.append(temp_rect)
                candidate_areas.append(area1+area2)
        # choose the largest area 
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print(candidate_areas)
            print(candidate_rects)
        a = sorted(range(len(candidate_areas)), key=lambda k: candidate_areas[k])
        bkgrnd_box = candidate_rects[a[len(a)-1]]
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('bkgrnd_box when > 4 lines')
            print(bkgrnd_box)
    else:         
        bkgrnd_box = []     
        for i in range(0,len(lines)-1):         
            for j in range(i+1,len(lines)):
                line1 = lines[i]; line2 = lines[j]
                # make sure they aren't parallel lines
                if np.abs(line1[1] - line2[1]) > 1e-4: 
                    # sometimes convention of only positive theta is not followed; ignore lines which have slopes very close to each other
                    if (np.abs(1/np.tan(line1[1])-1/np.tan(line2[1]))) > 1e-4: 
                        a = intersection(line1, line2)
                        #print('pts of intersection: ', a)
                        if (a[0] < sh_img[1] and a[0] > 0 and a[1] > 0 and a[1] < sh_img[0]):
                            bkgrnd_box.append(a)    
        # convert to list of numbers
        if (len(bkgrnd_box) > 0):
            a = np.asarray(bkgrnd_box,dtype=np.int64)
            b = []
            b.append(a)
            bkgrnd_box = b        
        # order the corners in bkgrnd_box
        if (np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][1]) >= np.linalg.norm(bkgrnd_box[0][0] - bkgrnd_box[0][2])):
            temp_pt = np.copy(bkgrnd_box[0][1])
            bkgrnd_box[0][1] = bkgrnd_box[0][2]
            bkgrnd_box[0][2] = temp_pt
        if (np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][2]) >= np.linalg.norm(bkgrnd_box[0][1] - bkgrnd_box[0][3])):
            temp_pt = np.copy(bkgrnd_box[0][2])
            bkgrnd_box[0][2] = bkgrnd_box[0][3]
            bkgrnd_box[0][3] = temp_pt        
    
    if len(bkgrnd_box) > 0: # make sure there are 4 corners
        if len(bkgrnd_box[0]) < 4:
            # if bkgrnd_box doesn't have corners then revert back to the one obtained from segmentation if it has four corners
            if len(bkgrnd_box_seg) > 0:
                if len(bkgrnd_box_seg[0]) == 4:    
                    bkgrnd_box = bkgrnd_box_seg
                else:
                    print('No background paper found anywhere :- 1 !!!')
            else:
                print('No background paper found anywhere :- 2 !!!')
    else: # if bkgrnd_box obtained from intersection of edge and segmented image gave nothing then revert to the previous one
        if len(bkgrnd_box_seg) > 0:
            if len(bkgrnd_box_seg[0]) == 4:    
                bkgrnd_box = bkgrnd_box_seg
            else:
                print('No background paper found anywhere :-3 !!!')
        else:
            print('No background paper found anywhere :-4 !!!')
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('final background trapezoid')
        print(len(bkgrnd_box[0]), bkgrnd_box)
    
    
    # apply the perspective transform to remove the shear/rotation etc.
    img_new, gray_new = get_perspective_transform(bkgrnd_box, sh_img, sh_img, img, gray)
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure();plt.imshow(img_new, cmap='gray')
    
    ##############################################################################################
    ################### Get Tool Outline #########################################################
    ##############################################################################################
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('############### Tool outline detection ####################')
    # a little Gaussian smoothing to remove spurious edges
    blur = cv2.GaussianBlur(gray_new,(3,3),0) 
    # compute the median of the single channel pixel intensities
    v = np.median(blur)
    # apply automatic Canny edge detection using the computed median
    # use larger SIGMA to get more edges
    lower = int(max(0, (1.0 - 0.75) * v))
    upper = int(min(255, (1.0 + 0.75) * v))
    #lower = 100
    #upper = 200
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('lower %d, upper %d for tool edge detection on transformed image' % (lower, upper))
    edges = cv2.Canny(gray_new,lower,upper)    
    # "closing" transformation        
    # kernel for "closing" transformation 
    kernel_close = np.ones((7,7),np.uint8)
    img_for_tool_outline_edge = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)       
    
    transformed_img = np.copy(img_new)
    transformed_img_with_cntrs_hulls = np.copy(img_new)
        
    # clean up the boundary a little bit
    img_for_tool_outline_edge = clean_up_boundary(img_for_tool_outline_edge)    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        plt.figure();plt.imshow(img_for_tool_outline_edge, cmap='gray')   
    
    # use the original (640 rows reshaped) image to find the edges for tool detection
    # this might be better than the image obtained from perspective transformation 
    # because we lose resolution. Do the perspective transform on the edge image thus obtained
    if USE_ORIGINAL_IMAGE == True:
        # a little Gaussian smoothing to remove spurious edges
        blur = cv2.GaussianBlur(gray,(3,3),0) 
        # compute the median of the single channel pixel intensities
        v = np.median(blur)
        # apply automatic Canny edge detection using the computed median
        # use larger SIGMA to get more edges
        lower = int(max(0, (1.0 - 2*SIGMA) * v))
        upper = int(min(255, (1.0 + 2*SIGMA) * v))
        #lower = 100
        #upper = 200
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('lower %d, upper %d for tool edge detection on original image' % (lower, upper))
        edges = cv2.Canny(gray,lower,upper) 
        # apply the perspective transform to remove the shear/rotation etc.
        img_new, edges_new = get_perspective_transform(bkgrnd_box, sh_img, sh_img, img, edges)
        
        # edges_new might not be completely binary due to perspective transformation
        # we can do thresholding to make it completely binary, but not needed perhaps
        
        # "closing" transformation        
        # kernel for "closing" transformation 
        kernel_close = np.ones((7,7),np.uint8)
        img_for_tool_outline_edge_new = cv2.morphologyEx(edges_new, cv2.MORPH_CLOSE, kernel_close)
        # clean up the boundary a little bit
        img_for_tool_outline_edge_new = clean_up_boundary(img_for_tool_outline_edge_new)
        
        # lets do some sanity checking -- whether we want to use this new edge image or not
        pruned_cntrs = get_pruned_cntrs(img_for_tool_outline_edge,img_new)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('pruned cntrs on transformed img: ', len(pruned_cntrs))
            if len(pruned_cntrs)>0:
                for c in pruned_cntrs:
                    print(cntrperi(c), cntrarea(c))            
        pruned_cntrs_new = get_pruned_cntrs(img_for_tool_outline_edge_new,img_new)
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            print('pruned cntrs on original img: ', len(pruned_cntrs_new))
            if len(pruned_cntrs_new)>0:
                for c in pruned_cntrs_new:                    
                    print(cntrperi(c), cntrarea(c))
        # if the length (perimeter) of the largest contour in the new edge image is more than twice
        # that of the same in the previous edge image, don't use the new edge image        
        if cntrperi(pruned_cntrs_new[0])/cntrperi(pruned_cntrs[0]) < 2:
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                print('using original image for tool edge detection...')
            img_for_tool_outline_edge = img_for_tool_outline_edge_new
                            
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            plt.figure();plt.imshow(img_for_tool_outline_edge, cmap='gray')
        
    # use the information in the segmented image tocombine with edge image for tool outline
    # this is useful when shadows are cast and the edge image doesn't give information about the
    # tool outline. segmentation will include the shadow which we can perhaps easily edit in the tool editor
    if USE_SEGMENTATION_TOOL_OUTLINE == True:
        # work on the image before the perspective transformation
        ret,th = cv2.threshold(gray,0,255,cv2.THRESH_BINARY+cv2.THRESH_OTSU)
        #img_seg = cv2.bitwise_not(th)        
        # "opening" transformation
        # kernel for "opening" transformation 
        kernel_open = np.ones((7,7),np.uint8)
        img_for_tool_outline_seg = cv2.morphologyEx(th, cv2.MORPH_OPEN, kernel_open)
        img_for_tool_outline_seg = cv2.bitwise_not(img_for_tool_outline_seg)
        # now do the perspective transformation
        img_new, img_for_tool_outline_seg_new = get_perspective_transform(bkgrnd_box, sh_img, sh_img, img, img_for_tool_outline_seg)
        # clean up the boundary a little bit
        img_for_tool_outline_seg = clean_up_boundary(img_for_tool_outline_seg_new) 
                
        # add/take the OR of the two images -- edge image and segmented image
        img_for_tool_outline = cv2.bitwise_or(img_for_tool_outline_edge, img_for_tool_outline_seg)
        
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            plt.figure();plt.imshow(img_for_tool_outline_seg, cmap='gray')
            plt.figure();plt.imshow(img_for_tool_outline, cmap='gray')
    else:
        img_for_tool_outline = img_for_tool_outline_edge
        
    # get the contours around the tools/objects
    #pruned_box, pruned_cntrs, pruned_hulls = get_pruned_cntrs(img_for_tool_outline,img_new)
    pruned_cntrs = get_pruned_cntrs(img_for_tool_outline,img_new)
    
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        print('pruned_cntrs %d' % (len(np.squeeze(pruned_cntrs))))
        #print(pruned_cntrs)
    if len(pruned_cntrs) == 0:
        print('Some sing is wong!!! Zero contours found!!!')
        # return empty sets/numbers
        return [],[],[],-1,-1,[],[], []
    else:
        if DEBUG == True and MYENVIRONMENT == 'PYTHON':
            img_copy = np.copy(img_new)
            for c in pruned_cntrs:
                hull = cv2.convexHull(c)      
                # draw the contours and hulls in an image to make sure things are going okay
                cv2.drawContours(img_copy, [c], -1, (0,255,0), 2) 
                cv2.drawContours(img_copy, [hull], -1, (255,0,255), 2)
            plt.figure();plt.imshow(img_copy, cmap='gray')        
                
        # get the contour of largest enclosed area 
        pruned_cntrs = get_largest_cntr(pruned_cntrs) # should have ideally only one contour fittng the tool        
    
        ##############################################################################################
        ################### Prepare things to be sent to the tool editor #############################
        ##############################################################################################
        
        myhulls = []
        mycntrs = []
        myoffsetX = []
        myoffsetY = []    
        for c in pruned_cntrs:     
            # get the convex hull
            hull = cv2.convexHull(c)  
            #print(len(np.squeeze(hull)), hull)
            
            # draw the contours and hulls in an image to make sure things are going okay
            cv2.drawContours(transformed_img_with_cntrs_hulls, [c], -1, (0,255,0), 2) 
            cv2.drawContours(transformed_img_with_cntrs_hulls, [hull], -1, (255,0,255), 2)
            if DEBUG == True and MYENVIRONMENT == 'PYTHON':
                plt.figure();plt.imshow(transformed_img_with_cntrs_hulls, cmap='gray')
            
            points = np.squeeze(c)
            points = points.astype(float)
            x_min = np.min(points[:,0])
            y_min = np.min(points[:,1])
            hull_points = np.squeeze(hull)
            hull_points = hull_points.astype(float)
            
            # scale to the A4 paper size and flatten them so that we output a 1-dim array
            cntr_pts = []
            hull_pts = []
            for i in range(0,len(points[:,0])):
                points[i,0] = round(float(points[i,0] - x_min)*float(A4_w/sh_img[1]),2)
                points[i,1] = round(float(points[i,1] - y_min)*float(A4_h/sh_img[0]),2)
                # convert numpy values to naive Python types; this is needed while transmitting back to the client
                cntr_pts.append(points[i,0].item())
                cntr_pts.append(points[i,1].item())
            for i in range(0,len(hull_points[:,0])):            
                hull_points[i,0] = round(float(hull_points[i,0] - x_min)*float(A4_w/sh_img[1]),2)        
                hull_points[i,1] = round(float(hull_points[i,1] - y_min)*float(A4_h/sh_img[0]),2)
                # convert numpy values to naive Python types; this is needed while transmitting back to the client
                hull_pts.append(hull_points[i,0].item())
                hull_pts.append(hull_points[i,1].item())
                
            # append the first point at the end to close the loop
            cntr_pts.append(points[0,0].item())
            cntr_pts.append(points[0,1].item())
            hull_pts.append(hull_points[0,0].item())
            hull_pts.append(hull_points[0,1].item())
            
            # create a list of the cntrs & hulls etc. if there are multiple objects in the image
            mycntrs.append(cntr_pts)
            myhulls.append(hull_pts)
            myoffsetX.append(x_min.item())
            myoffsetY.append(y_min.item())
        
        # delete for heroku # 
        cv2.imwrite(os.path.join(folder_jpg_results,fname),transformed_img_with_cntrs_hulls)
        #cv2.imwrite(os.path.join(folder_jpg_results,fname),transformed_img)
        
        #print(mycntrs, myhulls, myoffsetX, myoffsetY)
        
        # delete for heroku #  
        return transformed_img, mycntrs, myhulls, myoffsetX, myoffsetY, bkgrnd_box, sh_img, pruned_cntrs
        # insert for heroku #        
        #return transformed_img, mycntrs, myhulls, myoffsetX, myoffsetY

# delete main for heroku #
if __name__ == '__main__':    
  
    # folders for all the input data and output results    
    #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools new jpeg'
    #folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools jpeg'
    folder_jpg = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools trouble jpeg' 
    #folder_jpg = 'D://home//work//DigitalHome//DigitalToolBox//Test_input'
    #folder_jpg_results = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Debugging//Test_output'      
    folder_jpg_results = 'D://home//work//DigitalHome//DigitalToolBox//Test_output'      
    if DEBUG == True and MYENVIRONMENT == 'PYTHON':
        fname = 'IMG_5562.jpg'
        print(fname) 
        im = cv2.imread(os.path.join(folder_jpg,fname))
        cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, bkgrnd_box, sh_img, pruned_cntrs = get_contours(im) 
    else:
        for fname in os.listdir(folder_jpg):        
            if fname.endswith(".jpg") or fname.endswith(".jpeg") or fname.endswith(".JPG"): 
                print(fname) 
                im = cv2.imread(os.path.join(folder_jpg,fname))
                cvImage, myrawdata, myconvexhull, myoffsetX, myoffsetY, bkgrnd_box, sh_img, pruned_cntrs = get_contours(im)                   
                #cv2.imwrite(os.path.join(folder_jpg_results,fname),cvImagewithCntrs)                
                # write to fname.txt when testing mode is on
                if TESTING == True and MYENVIRONMENT == 'PYTHON': 
                    folder_txt_results = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//Tools test txt'
                    #folder_txt_results = 'D://home//work//DigitalHome//DigitalToolBox//Test_output'
                    filename = os.path.splitext(fname)[0]
                    file = open(os.path.join(folder_txt_results,filename+".txt"),'w')
                    # write the size of the original image
                    file.write('%d %d # orig image (rows,cols) # \n' % (sh_img[0],sh_img[1]))
                    # write the background box coordinates              
                    if len(bkgrnd_box) > 0:
                        for i in range(0,len(bkgrnd_box[0])):
                            file.write('%d %d ' % (bkgrnd_box[0][i,0],bkgrnd_box[0][i,1]))
                        file.write('# background paper coordinates # \n')
                    else:
                        file.write('-1 # background paper coordinates # \n')
                    # write the number of tools 
                    file.write('%d # number of tools # \n' % (len(pruned_cntrs)))
                    # write the offsetx and offsety
                    for i in range(0,len(myoffsetX)):
                        file.write('%.2f %.2f # offsetX and offsetY # \n' % (myoffsetX[i], myoffsetY[i]))
                    # write the convex hulls
                    i = 0
                    for c in pruned_cntrs:
                        # get the convex hull
                        hull = cv2.convexHull(c) 
                        hull = np.squeeze(hull)
                        file.write('%d ' % (len(hull)))
                        file.write('# number of points in convexhull no. %d # \n' % (i+1))
                        for j in range(0,len(hull)):
                            file.write('%.2f %.2f ' % (hull[j][0], hull[j][1]))
                        file.write('# convexhull no. %d # \n' % (i+1))
                        i = i+1
                    # write the tool path
                    i = 0
                    for c in pruned_cntrs:  
                        cntr = np.squeeze(c)
                        file.write('%d %.2f %.2f ' % (len(cntr), cntrperi(cntr), cntrarea(cntr)))
                        file.write('# number of points, perimeter, area of contour no. %d # \n' % (i+1))                                  
                        for j in range(0,len(cntr)):
                            file.write('%.2f %.2f ' % (cntr[j][0], cntr[j][1]))
                        file.write('# contour no. %d # \n' % (i+1))
                    file.close()
            else:
                print("Error: Need input files to be in jpeg format")


