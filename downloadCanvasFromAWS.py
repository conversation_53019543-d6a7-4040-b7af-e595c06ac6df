#postgres library
import psycopg2
import datetime
from datetime import date
import string
import random
import os, boto3
from botocore.exceptions import NoCredentialsError
import time
import sys
from botocore.exceptions import ClientError

# Amazon S3 access    
ACCESS_KEY = '********************'
SECRET_KEY = '4/EcoiSTPxQASMv0ssJJT9b96mnVSaPvueCpybBJ'
S3_BUCKET_NAME = 'toolkaiser'
toolKaiserID = "OpaEsdoXAJ" # Jared's id is HPXhprEVRM; # if this is empty "", then all the images in public/ are downloaded to dropbox
FOLDER = 'D://home//Dropbox//TheOneBillionDollarStartup//Data//AWS_Images'
#FOLDER = '/Users/<USER>/Dropbox/TheOneBillionDollarStartup/Data/AWS_Images'
past = 5 # get data X days back
DATABASE_URL = "postgres://xwjlcqdzbdcffx:<EMAIL>:5432/dcejk9q4ujbmld"  
  
# function to download file from S3 and save to local_file
def download_from_aws(local_file, bucket, s3_file):
    s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    
    try:
        s3.download_file(bucket, s3_file, local_file)
        print("Download Successful")
        return True
    except FileNotFoundError:
        print("The file was not found")
        return False
    except NoCredentialsError:
        print("Credentials not available")
        return False

# function to delete a file from S3
def delete_s3_file(bucket, s3_file):    
    s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    s3.delete_object(Bucket=bucket, Key=s3_file)
    print("Deleting %s" % s3_file)    

# check if file exists on s3
def check(bucket, key):
    s3 = boto3.client('s3', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)
    try:
        s3.head_object(Bucket=bucket, Key=key)
    except ClientError as e:
        return int(e.response['Error']['Code']) != 404
    return True

if __name__ == '__main__':
    
    if toolKaiserID == "":
        
        today = datetime.datetime.now()
        d = datetime.timedelta(days = past)
        # get the date past days ago
        pastDay = today - d
        
        # empty toolkaiser id implies download all the photos; no need to go via the database
        # initiate s3 resource    
        s3 = boto3.resource('s3',aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY)

        # select bucket
        my_bucket = s3.Bucket('toolkaiser')
        print(my_bucket)
        # download file into current directory
        for s3_object in my_bucket.objects.filter(Prefix = 'public'):
            # Need to split s3_object.key into path and file name, else it will give error file not found.
            if s3_object.key == 'public/':
                continue
            path, filename = os.path.split(s3_object.key)            
            full_filename = os.path.join(FOLDER,filename)            
            if (s3_object.last_modified).date() > pastDay.date():                  
                print('downloading file = ', s3_object.key, (s3_object.last_modified).date())
                try:
                    my_bucket.download_file(s3_object.key, full_filename)
                except:
                    print('error occured while downloading')
    else:
        # download only those photos corresponding to the user id
        # if toolkaiser id is provided, then we first need to connect to the local database
        # make sure it is running first as follows:
        #   run 'pgadmin' from windows
        #   Go to C:\; start local database by running command: postgres -D "C:/Program Files/PostgreSQL/12/data"
        # delete the myLocalUserDataDB and then pull the heroku database into it by following command:
        #   Go to C:\; set PGUSER=postgres; set PGPASSWORD=Kudekar; heroku pg:pull postgresql-rectangular-89670 myLocalUserDataDB --app toolkaiser
        # go to pgadmin and check if you can see the myLocalUserDataDB and its content
        
        # connect to the local database
        #con = psycopg2.connect(host="localhost", database="myLocalUserDataDB", user="postgres", password="Kudekar", port=5432)
        con = psycopg2.connect(DATABASE_URL, sslmode='require')
         # cursor
        cur = con.cursor()
        
        # use this command to get photoIDs with the given toolkaiser id 
        cur.execute("SELECT id, type FROM userDataDB WHERE toolKaiserid = "+"\'"+toolKaiserID+"\'") 
        
        # this is a list of tuples (rownumber, photoID)
        rows = cur.fetchall()
        
        # commit the transaction otherwise you will not see it in pgadmin
        con.commit()
    
        # close the cursor
        cur.close()
    
        # close the connection
        con.close()
                  
        #photoIDs = []
        for r in rows:
            photoID = r[0]
            if r[1] == 'c':
                full_filename = os.path.join(FOLDER,photoID+'.json')
                print('downloading file = ', full_filename)
                downloaded = download_from_aws(full_filename, S3_BUCKET_NAME, 'public/'+photoID+'.json')
